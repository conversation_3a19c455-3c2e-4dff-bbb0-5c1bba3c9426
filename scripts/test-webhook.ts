const testPayload = {
  event: "call_ended",
  call: {
    call_id: "call_bb915c78daa96954cd1a025b16c",
    agent_id: "agent_2b0610a06ac2082f8a5c29ed84", // <PERSON><PERSON>_Demo agent
    call_status: "ended",
    start_timestamp: 1736795076115,
    end_timestamp: 1736795201931,
    duration_ms: 125816,
    retell_llm_dynamic_variables: { customer_name: "<PERSON> Fool<PERSON>" },
    transcript: "Hello, this is a test call transcript.",
    disconnection_reason: "user_hangup"
  }
}

async function testWebhook() {
  console.log('Testing webhook with payload:', JSON.stringify(testPayload, null, 2))

  const response = await fetch('http://localhost:3001/api/webhook/retell', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(testPayload)
  })

  const data = await response.json()
  console.log('Response status:', response.status)
  console.log('Response:', data)
}

testWebhook().catch(console.error)