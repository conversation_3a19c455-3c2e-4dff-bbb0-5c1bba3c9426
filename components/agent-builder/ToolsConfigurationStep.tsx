"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
  Trash2, 
  Phone, 
  PhoneCall, 
  Calendar, 
  Hash, 
  Webhook, 
  AlertCircle,
  CheckCircle,
  Wrench
} from "lucide-react";
import { AgentTemplate } from "@/lib/agent-templates";

// Tool type definitions
export interface Tool {
  id: string;
  type: 'end_call' | 'transfer_call' | 'custom' | 'press_digit' | 'check_availability_cal' | 'book_appointment_cal';
  name: string;
  description: string;
  enabled: boolean;
  // Tool-specific configurations
  transfer_destination?: {
    type: 'predefined' | 'inferred';
    value?: 'voicemail' | 'operator';
    number?: string;
    prompt?: string;
  };
  digit?: string; // For press_digit
  url?: string; // For custom tools
  speak_after_execution?: boolean;
  speak_during_execution?: boolean;
  // Calendar tool specific
  calendar_url?: string;
  cal_api_key?: string;
  event_type_id?: number;
}

interface ToolsConfigurationStepProps {
  selectedTemplate: AgentTemplate | null;
  tools: Tool[];
  onToolsChange: (tools: Tool[]) => void;
  onNext: () => void;
  onBack: () => void;
}

// Predefined tool templates
const TOOL_TEMPLATES: Omit<Tool, 'id' | 'enabled'>[] = [
  {
    type: 'end_call',
    name: 'End Call',
    description: 'Allow the agent to end the conversation when appropriate'
  },
  {
    type: 'transfer_call',
    name: 'Transfer to Human Agent',
    description: 'Transfer the call to a human representative',
    transfer_destination: {
      type: 'predefined',
      value: 'operator',
      number: ''
    }
  },
  {
    type: 'transfer_call',
    name: 'Transfer to Voicemail',
    description: 'Transfer the call to voicemail system',
    transfer_destination: {
      type: 'predefined',
      value: 'voicemail',
      number: ''
    }
  },
  {
    type: 'press_digit',
    name: 'Press Digit',
    description: 'Navigate IVR systems by pressing digits',
    digit: '1'
  },
  {
    type: 'check_availability_cal',
    name: 'Check Calendar Availability',
    description: 'Check available appointment slots',
    calendar_url: '',
    cal_api_key: '',
    event_type_id: 0
  },
  {
    type: 'book_appointment_cal',
    name: 'Book Appointment',
    description: 'Schedule appointments with customers',
    calendar_url: '',
    cal_api_key: '',
    event_type_id: 0
  },
  {
    type: 'custom',
    name: 'Custom Webhook',
    description: 'Call a custom API endpoint',
    url: '',
    speak_after_execution: true,
    speak_during_execution: false
  }
];

const getToolIcon = (type: string) => {
  switch (type) {
    case 'end_call': return Phone;
    case 'transfer_call': return PhoneCall;
    case 'press_digit': return Hash;
    case 'check_availability_cal':
    case 'book_appointment_cal': return Calendar;
    case 'custom': return Webhook;
    default: return Wrench;
  }
};

const getToolColor = (type: string) => {
  switch (type) {
    case 'end_call': return 'bg-red-100 text-red-800 border-red-200';
    case 'transfer_call': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'press_digit': return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'check_availability_cal':
    case 'book_appointment_cal': return 'bg-green-100 text-green-800 border-green-200';
    case 'custom': return 'bg-orange-100 text-orange-800 border-orange-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export default function ToolsConfigurationStep({
  selectedTemplate,
  tools,
  onToolsChange,
  onNext,
  onBack
}: ToolsConfigurationStepProps) {
  const [selectedTools, setSelectedTools] = useState<Tool[]>(tools);

  // Initialize with template tools if available
  useEffect(() => {
    if (selectedTemplate?.tools && selectedTools.length === 0) {
      const templateTools: Tool[] = selectedTemplate.tools.map((tool, index) => ({
        id: `template-${index}`,
        type: tool.type as Tool['type'],
        name: tool.name,
        description: tool.description,
        enabled: true,
        ...tool.config
      }));
      setSelectedTools(templateTools);
      onToolsChange(templateTools);
    }
  }, [selectedTemplate, selectedTools.length, onToolsChange]);

  const addTool = (template: Omit<Tool, 'id' | 'enabled'>) => {
    const newTool: Tool = {
      ...template,
      id: `tool-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      enabled: true
    };
    const updatedTools = [...selectedTools, newTool];
    setSelectedTools(updatedTools);
    onToolsChange(updatedTools);
  };

  const removeTool = (toolId: string) => {
    const updatedTools = selectedTools.filter(tool => tool.id !== toolId);
    setSelectedTools(updatedTools);
    onToolsChange(updatedTools);
  };

  const updateTool = (toolId: string, updates: Partial<Tool>) => {
    const updatedTools = selectedTools.map(tool =>
      tool.id === toolId ? { ...tool, ...updates } : tool
    );
    setSelectedTools(updatedTools);
    onToolsChange(updatedTools);
  };

  const toggleTool = (toolId: string) => {
    updateTool(toolId, { enabled: !selectedTools.find(t => t.id === toolId)?.enabled });
  };

  const enabledToolsCount = selectedTools.filter(tool => tool.enabled).length;

  return (
    <div className="max-w-6xl mx-auto py-8 px-6">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">Configure Agent Tools</h2>
        <p className="text-gray-300 text-lg">
          Add function calling capabilities to your agent. Tools allow your agent to take actions like ending calls, 
          transferring to humans, booking appointments, and more.
        </p>
      </div>

      {selectedTemplate && selectedTemplate.tools && selectedTemplate.tools.length > 0 && (
        <Card className="bg-blue-900/20 border-blue-800 mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <span className="text-xl mr-2">{selectedTemplate.icon}</span>
              Recommended Tools for {selectedTemplate.name}
            </CardTitle>
            <CardDescription className="text-gray-300">
              Your template includes these pre-configured tools
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {selectedTemplate.tools.map((tool, index) => (
                <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                  {tool.name}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Available Tools */}
        <div className="lg:col-span-1">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Available Tools</CardTitle>
              <CardDescription className="text-gray-300">
                Click to add tools to your agent
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {TOOL_TEMPLATES.map((template, index) => {
                const Icon = getToolIcon(template.type);
                const isAlreadyAdded = selectedTools.some(tool => 
                  tool.type === template.type && tool.name === template.name
                );
                
                return (
                  <Button
                    key={index}
                    variant="outline"
                    className={`w-full p-4 h-auto text-left justify-start ${
                      isAlreadyAdded 
                        ? 'bg-gray-800 border-gray-600 opacity-50 cursor-not-allowed' 
                        : 'bg-gray-800 border-gray-700 hover:bg-gray-700'
                    }`}
                    onClick={() => !isAlreadyAdded && addTool(template)}
                    disabled={isAlreadyAdded}
                  >
                    <div className="flex items-start space-x-3 w-full">
                      <Icon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-white text-sm">{template.name}</div>
                        <div className="text-xs text-gray-400 mt-1">{template.description}</div>
                        {isAlreadyAdded && (
                          <div className="flex items-center mt-1">
                            <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                            <span className="text-xs text-green-500">Added</span>
                          </div>
                        )}
                      </div>
                      {!isAlreadyAdded && <Plus className="h-4 w-4 text-gray-400" />}
                    </div>
                  </Button>
                );
              })}
            </CardContent>
          </Card>
        </div>

        {/* Configured Tools */}
        <div className="lg:col-span-2">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span>Configured Tools ({enabledToolsCount} enabled)</span>
                {enabledToolsCount === 0 && (
                  <Badge variant="outline" className="text-yellow-500 border-yellow-500">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    No tools enabled
                  </Badge>
                )}
              </CardTitle>
              <CardDescription className="text-gray-300">
                Configure and enable the tools your agent can use
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedTools.length === 0 ? (
                <div className="text-center py-8">
                  <Wrench className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400 mb-2">No tools configured yet</p>
                  <p className="text-sm text-gray-500">Add tools from the left panel to get started</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {selectedTools.map((tool) => {
                    const Icon = getToolIcon(tool.type);
                    const colorClass = getToolColor(tool.type);
                    
                    return (
                      <Card key={tool.id} className={`border-gray-700 ${tool.enabled ? 'bg-gray-800' : 'bg-gray-800/50'}`}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <Icon className="h-5 w-5 text-gray-400" />
                              <div>
                                <h4 className="font-medium text-white">{tool.name}</h4>
                                <Badge variant="outline" className={`text-xs ${colorClass}`}>
                                  {tool.type.replace('_', ' ')}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={tool.enabled}
                                onCheckedChange={() => toggleTool(tool.id)}
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeTool(tool.id)}
                                className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          
                          <div className="space-y-3">
                            <div>
                              <Label htmlFor={`desc-${tool.id}`} className="text-sm text-gray-300">Description</Label>
                              <Textarea
                                id={`desc-${tool.id}`}
                                value={tool.description}
                                onChange={(e) => updateTool(tool.id, { description: e.target.value })}
                                className="mt-1 bg-gray-700 border-gray-600 text-white"
                                rows={2}
                              />
                            </div>

                            {/* Tool-specific configuration */}
                            {tool.type === 'transfer_call' && (
                              <div className="grid grid-cols-2 gap-3">
                                <div>
                                  <Label className="text-sm text-gray-300">Transfer Type</Label>
                                  <Select
                                    value={tool.transfer_destination?.type || 'predefined'}
                                    onValueChange={(value) => updateTool(tool.id, {
                                      transfer_destination: { ...tool.transfer_destination, type: value as 'predefined' | 'inferred' }
                                    })}
                                  >
                                    <SelectTrigger className="bg-gray-700 border-gray-600">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="predefined">Predefined Number</SelectItem>
                                      <SelectItem value="inferred">AI Inferred</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <Label className="text-sm text-gray-300">Phone Number</Label>
                                  <Input
                                    value={tool.transfer_destination?.number || ''}
                                    onChange={(e) => updateTool(tool.id, {
                                      transfer_destination: { ...tool.transfer_destination, number: e.target.value }
                                    })}
                                    placeholder="+1234567890"
                                    className="bg-gray-700 border-gray-600 text-white"
                                  />
                                </div>
                              </div>
                            )}

                            {tool.type === 'press_digit' && (
                              <div>
                                <Label className="text-sm text-gray-300">Digit to Press</Label>
                                <Input
                                  value={tool.digit || ''}
                                  onChange={(e) => updateTool(tool.id, { digit: e.target.value })}
                                  placeholder="1"
                                  maxLength={1}
                                  className="bg-gray-700 border-gray-600 text-white"
                                />
                              </div>
                            )}

                            {(tool.type === 'check_availability_cal' || tool.type === 'book_appointment_cal') && (
                              <div className="grid grid-cols-1 gap-3">
                                <div>
                                  <Label className="text-sm text-gray-300">Calendar URL</Label>
                                  <Input
                                    value={tool.calendar_url || ''}
                                    onChange={(e) => updateTool(tool.id, { calendar_url: e.target.value })}
                                    placeholder="https://cal.com/your-calendar"
                                    className="bg-gray-700 border-gray-600 text-white"
                                  />
                                </div>
                                <div className="grid grid-cols-2 gap-3">
                                  <div>
                                    <Label className="text-sm text-gray-300">API Key</Label>
                                    <Input
                                      value={tool.cal_api_key || ''}
                                      onChange={(e) => updateTool(tool.id, { cal_api_key: e.target.value })}
                                      placeholder="cal_api_key"
                                      type="password"
                                      className="bg-gray-700 border-gray-600 text-white"
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-sm text-gray-300">Event Type ID</Label>
                                    <Input
                                      value={tool.event_type_id || ''}
                                      onChange={(e) => updateTool(tool.id, { event_type_id: parseInt(e.target.value) || 0 })}
                                      placeholder="123456"
                                      type="number"
                                      className="bg-gray-700 border-gray-600 text-white"
                                    />
                                  </div>
                                </div>
                              </div>
                            )}

                            {tool.type === 'custom' && (
                              <div className="space-y-3">
                                <div>
                                  <Label className="text-sm text-gray-300">Webhook URL</Label>
                                  <Input
                                    value={tool.url || ''}
                                    onChange={(e) => updateTool(tool.id, { url: e.target.value })}
                                    placeholder="https://api.example.com/webhook"
                                    className="bg-gray-700 border-gray-600 text-white"
                                  />
                                </div>
                                <div className="flex items-center space-x-4">
                                  <div className="flex items-center space-x-2">
                                    <Switch
                                      checked={tool.speak_during_execution || false}
                                      onCheckedChange={(checked) => updateTool(tool.id, { speak_during_execution: checked })}
                                    />
                                    <Label className="text-sm text-gray-300">Speak during execution</Label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Switch
                                      checked={tool.speak_after_execution || false}
                                      onCheckedChange={(checked) => updateTool(tool.id, { speak_after_execution: checked })}
                                    />
                                    <Label className="text-sm text-gray-300">Speak after execution</Label>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <Separator className="my-8 bg-gray-800" />

      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="text-center">
          <p className="text-sm text-gray-400 mb-2">
            {enabledToolsCount > 0 
              ? `${enabledToolsCount} tool${enabledToolsCount === 1 ? '' : 's'} enabled`
              : 'Tools are optional but recommended for advanced functionality'
            }
          </p>
        </div>

        <Button
          onClick={onNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
