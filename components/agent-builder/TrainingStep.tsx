"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { ChevronLeft, ChevronRight, Plus, Trash2, User, Lightbulb } from "lucide-react";
import { VoiceAgentIcon } from "@/components/ui/voice-agent-icon";
import { AgentTemplate } from "@/lib/agent-templates";

interface TrainingExample {
  id: string;
  scenario: string;
  userInput: string;
  expectedResponse: string;
}

interface TrainingStepProps {
  selectedTemplate: AgentTemplate | null;
  onNext: () => void;
  onBack: () => void;
}

export default function TrainingStep({
  selectedTemplate,
  onNext,
  onBack
}: TrainingStepProps) {
  const [customExamples, setCustomExamples] = useState<TrainingExample[]>([]);
  const [newExample, setNewExample] = useState({
    scenario: '',
    userInput: '',
    expectedResponse: ''
  });

  const addCustomExample = () => {
    if (newExample.scenario && newExample.userInput && newExample.expectedResponse) {
      const example: TrainingExample = {
        id: crypto.randomUUID(),
        ...newExample
      };
      setCustomExamples([...customExamples, example]);
      setNewExample({ scenario: '', userInput: '', expectedResponse: '' });
    }
  };

  const removeCustomExample = (id: string) => {
    setCustomExamples(customExamples.filter(ex => ex.id !== id));
  };

  const templateExamples = selectedTemplate?.trainingExamples || [];

  return (
    <div className="max-w-5xl mx-auto py-8">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">Training Examples</h2>
        <p className="text-gray-300 text-lg mb-6">
          Provide example conversations to help your AI agent learn how to respond appropriately.
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Template Examples */}
            {selectedTemplate && templateExamples.length > 0 && (
              <Card className="bg-blue-900/20 border-blue-800">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <span className="text-xl mr-2">{selectedTemplate.icon}</span>
                    Template Training Examples
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Pre-configured examples from your {selectedTemplate.name} template
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {templateExamples.map((example, index) => (
                      <Card key={index} className="bg-gray-800 border-gray-700">
                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div>
                              <Badge variant="secondary" className="bg-purple-100 text-purple-800 mb-2">
                                {example.scenario}
                              </Badge>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-start space-x-2">
                                <User className="h-4 w-4 text-blue-400 mt-1 flex-shrink-0" />
                                <div className="bg-gray-700 rounded-lg p-3 flex-1">
                                  <p className="text-gray-300 text-sm">{example.userInput}</p>
                                </div>
                              </div>
                              <div className="flex items-start space-x-2">
                                <VoiceAgentIcon className="h-4 w-4 text-green-400 mt-1 flex-shrink-0" />
                                <div className="bg-green-900/20 border border-green-800 rounded-lg p-3 flex-1">
                                  <p className="text-gray-300 text-sm">{example.expectedResponse}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Custom Examples */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Custom Training Examples</CardTitle>
                <CardDescription className="text-gray-300">
                  Add your own training examples for specific scenarios
                </CardDescription>
              </CardHeader>
              <CardContent>
                {customExamples.length > 0 && (
                  <div className="space-y-4 mb-6">
                    {customExamples.map((example) => (
                      <Card key={example.id} className="bg-gray-800 border-gray-700">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-3">
                            <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                              {example.scenario}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeCustomExample(example.id)}
                              className="text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-start space-x-2">
                              <User className="h-4 w-4 text-blue-400 mt-1 flex-shrink-0" />
                              <div className="bg-gray-700 rounded-lg p-3 flex-1">
                                <p className="text-gray-300 text-sm">{example.userInput}</p>
                              </div>
                            </div>
                            <div className="flex items-start space-x-2">
                              <VoiceAgentIcon className="h-4 w-4 text-green-400 mt-1 flex-shrink-0" />
                              <div className="bg-green-900/20 border border-green-800 rounded-lg p-3 flex-1">
                                <p className="text-gray-300 text-sm">{example.expectedResponse}</p>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                {/* Add New Example Form */}
                <Card className="bg-gray-800 border-gray-700 border-dashed">
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div>
                        <label className="text-white text-sm font-medium mb-2 block">
                          Scenario Description
                        </label>
                        <Textarea
                          placeholder="e.g., Customer asking about claim status"
                          value={newExample.scenario}
                          onChange={(e) => setNewExample(prev => ({ ...prev, scenario: e.target.value }))}
                          className="bg-gray-700 border-gray-600 text-white h-20"
                        />
                      </div>
                      <div>
                        <label className="text-white text-sm font-medium mb-2 flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          Customer Input
                        </label>
                        <Textarea
                          placeholder="What the customer might say..."
                          value={newExample.userInput}
                          onChange={(e) => setNewExample(prev => ({ ...prev, userInput: e.target.value }))}
                          className="bg-gray-700 border-gray-600 text-white h-20"
                        />
                      </div>
                      <div>
                        <label className="text-white text-sm font-medium mb-2 flex items-center">
                          <VoiceAgentIcon className="h-4 w-4 mr-1" />
                          Expected Agent Response
                        </label>
                        <Textarea
                          placeholder="How the agent should respond..."
                          value={newExample.expectedResponse}
                          onChange={(e) => setNewExample(prev => ({ ...prev, expectedResponse: e.target.value }))}
                          className="bg-gray-700 border-gray-600 text-white h-24"
                        />
                      </div>
                      <Button
                        onClick={addCustomExample}
                        disabled={!newExample.scenario || !newExample.userInput || !newExample.expectedResponse}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Training Example
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card className="bg-yellow-900/20 border-yellow-800">
              <CardHeader>
                <CardTitle className="text-yellow-400 flex items-center text-lg">
                  <Lightbulb className="h-5 w-5 mr-2" />
                  Training Tips
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-yellow-200">
                  <strong>Quality over Quantity:</strong> A few well-crafted examples are better than many poor ones.
                </div>
                <div className="text-sm text-yellow-200">
                  <strong>Cover Edge Cases:</strong> Include examples of difficult or unusual scenarios.
                </div>
                <div className="text-sm text-yellow-200">
                  <strong>Be Specific:</strong> Show exactly how the agent should handle different situations.
                </div>
                <div className="text-sm text-yellow-200">
                  <strong>Template Examples:</strong> Your template already includes proven examples that work well.
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white text-lg">Training Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Template Examples:</span>
                    <span className="text-white">{templateExamples.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Custom Examples:</span>
                    <span className="text-white">{customExamples.length}</span>
                  </div>
                  <div className="flex justify-between text-sm font-medium border-t border-gray-700 pt-2">
                    <span className="text-gray-300">Total Examples:</span>
                    <span className="text-white">{templateExamples.length + customExamples.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {!selectedTemplate && (
              <Card className="bg-orange-900/20 border-orange-800">
                <CardHeader>
                  <CardTitle className="text-orange-400 text-lg">No Template Selected</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-orange-200 text-sm">
                    Without a template, you&apos;ll need to create all training examples from scratch.
                    Consider going back to select a template for pre-built examples.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center pt-6 border-t border-gray-800">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="text-center">
          <p className="text-sm text-gray-400 mb-2">
            {templateExamples.length + customExamples.length > 0
              ? `${templateExamples.length + customExamples.length} training examples ready`
              : 'Training examples are optional but recommended'
            }
          </p>
        </div>

        <Button
          onClick={onNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
