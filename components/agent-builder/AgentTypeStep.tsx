"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronLeft, 
  ChevronRight, 
  Phone, 
  Monitor, 
  MessageCircle, 
  Layers,
  Check,
  Info
} from "lucide-react";
import { AgentTemplate } from "@/lib/agent-templates";

export type AgentType = 'voice_call' | 'web_call' | 'chat' | 'multi_modal';

interface AgentTypeOption {
  id: AgentType;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  features: string[];
  useCases: string[];
  color: string;
  isRecommended?: boolean;
  isPopular?: boolean;
}

interface AgentTypeStepProps {
  selectedTemplate: AgentTemplate | null;
  selectedType: AgentType;
  onTypeChange: (type: AgentType) => void;
  onNext: () => void;
  onBack: () => void;
}

const AGENT_TYPE_OPTIONS: AgentTypeOption[] = [
  {
    id: 'voice_call',
    name: 'Voice Call Agent',
    description: 'Traditional phone-based voice conversations with customers',
    icon: Phone,
    features: [
      'Inbound & outbound phone calls',
      'Real-time voice interaction',
      'Call transfer capabilities',
      'Voicemail detection',
      'DTMF support'
    ],
    useCases: [
      'Customer service hotlines',
      'Sales outreach calls',
      'Appointment scheduling',
      'Insurance claims processing',
      'Support escalation'
    ],
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    isRecommended: true,
    isPopular: true
  },
  {
    id: 'web_call',
    name: 'Web Call Agent',
    description: 'Browser-based voice calls embedded in websites',
    icon: Monitor,
    features: [
      'Browser-based voice calls',
      'No phone number required',
      'Easy website integration',
      'Real-time voice interaction',
      'Screen sharing support'
    ],
    useCases: [
      'Website customer support',
      'Online consultations',
      'Virtual assistance',
      'E-commerce support',
      'Lead qualification'
    ],
    color: 'bg-green-100 text-green-800 border-green-200',
    isPopular: true
  },
  {
    id: 'chat',
    name: 'Chat Agent',
    description: 'Text-based conversations for messaging platforms',
    icon: MessageCircle,
    features: [
      'Text-based conversations',
      'Multi-platform support',
      'Rich message formatting',
      'File sharing capabilities',
      'Conversation history'
    ],
    useCases: [
      'Website chat widgets',
      'SMS conversations',
      'Social media messaging',
      'Support tickets',
      'FAQ automation'
    ],
    color: 'bg-purple-100 text-purple-800 border-purple-200'
  },
  {
    id: 'multi_modal',
    name: 'Multi-Modal Agent',
    description: 'Supports both voice and chat interactions seamlessly',
    icon: Layers,
    features: [
      'Voice + chat capabilities',
      'Seamless mode switching',
      'Cross-platform deployment',
      'Unified conversation history',
      'Advanced interaction options'
    ],
    useCases: [
      'Comprehensive customer support',
      'Enterprise solutions',
      'Omnichannel experiences',
      'Complex workflows',
      'Premium service offerings'
    ],
    color: 'bg-orange-100 text-orange-800 border-orange-200'
  }
];

export default function AgentTypeStep({
  selectedTemplate,
  selectedType,
  onTypeChange,
  onNext,
  onBack
}: AgentTypeStepProps) {
  const [localSelectedType, setLocalSelectedType] = useState<AgentType>(selectedType);

  // Update local state when prop changes
  useEffect(() => {
    setLocalSelectedType(selectedType);
  }, [selectedType]);

  const handleTypeSelect = (type: AgentType) => {
    setLocalSelectedType(type);
    onTypeChange(type);
  };

  const selectedOption = AGENT_TYPE_OPTIONS.find(option => option.id === localSelectedType);

  return (
    <div className="max-w-6xl mx-auto py-8 px-6">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">Choose Agent Type</h2>
        <p className="text-gray-300 text-lg">
          Select how customers will interact with your AI agent. Each type offers different capabilities 
          and deployment options.
        </p>
      </div>

      {selectedTemplate && (
        <Card className="bg-blue-900/20 border-blue-800 mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <span className="text-xl mr-2">{selectedTemplate.icon}</span>
              Template Recommendation: {selectedTemplate.name}
            </CardTitle>
            <CardDescription className="text-gray-300">
              Based on your template selection, we recommend starting with a Voice Call Agent for insurance use cases.
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {AGENT_TYPE_OPTIONS.map((option) => {
          const Icon = option.icon;
          const isSelected = localSelectedType === option.id;
          
          return (
            <Card 
              key={option.id}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected 
                  ? 'bg-blue-900/30 border-blue-500 ring-2 ring-blue-500/50' 
                  : 'bg-gray-900 border-gray-800 hover:border-gray-700 hover:bg-gray-800'
              }`}
              onClick={() => handleTypeSelect(option.id)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${option.color}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-white flex items-center">
                        {option.name}
                        {option.isRecommended && (
                          <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                            Recommended
                          </Badge>
                        )}
                        {option.isPopular && (
                          <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                            Popular
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription className="text-gray-300">
                        {option.description}
                      </CardDescription>
                    </div>
                  </div>
                  {isSelected && (
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <Check className="h-4 w-4 text-white" />
                      </div>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-2">Key Features</h4>
                    <ul className="space-y-1">
                      {option.features.map((feature, index) => (
                        <li key={index} className="text-sm text-gray-400 flex items-center">
                          <div className="w-1.5 h-1.5 bg-gray-500 rounded-full mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-2">Common Use Cases</h4>
                    <div className="flex flex-wrap gap-1">
                      {option.useCases.slice(0, 3).map((useCase, index) => (
                        <Badge 
                          key={index} 
                          variant="outline" 
                          className="text-xs text-gray-400 border-gray-600"
                        >
                          {useCase}
                        </Badge>
                      ))}
                      {option.useCases.length > 3 && (
                        <Badge variant="outline" className="text-xs text-gray-400 border-gray-600">
                          +{option.useCases.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedOption && (
        <Card className="bg-gray-900 border-gray-800 mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Info className="h-5 w-5 mr-2 text-blue-400" />
              Selected: {selectedOption.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-3">All Features</h4>
                <ul className="space-y-2">
                  {selectedOption.features.map((feature, index) => (
                    <li key={index} className="text-sm text-gray-400 flex items-center">
                      <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-3">Perfect For</h4>
                <ul className="space-y-2">
                  {selectedOption.useCases.map((useCase, index) => (
                    <li key={index} className="text-sm text-gray-400 flex items-center">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 flex-shrink-0" />
                      {useCase}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-gray-700 text-white hover:bg-gray-800"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="text-center">
          <p className="text-sm text-gray-400">
            You can change the agent type later in settings
          </p>
        </div>

        <Button
          onClick={onNext}
          disabled={!localSelectedType}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next Step
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
