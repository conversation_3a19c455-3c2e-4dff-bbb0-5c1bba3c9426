"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { Progress } from "@/components/ui/progress";
import { CheckCircle, Loader2, AlertCircle, Play, Phone, Globe, Settings, Brain, Sparkles } from "lucide-react";
import { AgentTemplate } from "@/lib/agent-templates";
import { useToast } from "@/components/ui/use-toast";
import { bundleAgentWizardData, OptimizedAgentConfig } from "@/lib/openai-agent-optimizer";

interface AgentCreationStepProps {
  agentData: {
    name: string;
    description: string;
    agentType?: string; // 'voice_call', 'web_call', 'chat', 'multi_modal'
    voiceId: string;
    behavior: string;
    selectedTemplate?: AgentTemplate | null;
    selectedVoice?: any; // Voice object with additional metadata
    personalityTraits?: string[];
    communicationStyle?: string;
    customInstructions?: string;
    knowledgeBaseSelections?: string[];
    tools?: any[]; // Tools configuration
    trainingExamples?: any[];
  };
  onSuccess: (agentId: string) => void;
  onBack: () => void;
}

interface CreationStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  errorMessage?: string;
}

export default function AgentCreationStep({
  agentData,
  onSuccess,
  onBack
}: AgentCreationStepProps) {
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);
  const [creationSteps, setCreationSteps] = useState<CreationStep[]>([
    {
      id: 'optimization',
      name: 'AI Configuration Optimization',
      description: 'Using OpenAI to optimize your agent configuration for best performance',
      status: 'pending'
    },
    {
      id: 'llm',
      name: 'Creating LLM Configuration',
      description: 'Setting up the optimized language model configuration',
      status: 'pending'
    },
    {
      id: 'agent',
      name: 'Creating AI Agent',
      description: 'Configuring your agent with optimized voice and LLM settings',
      status: 'pending'
    },
    {
      id: 'organization',
      name: 'Adding to Organization',
      description: 'Registering the agent with your organization',
      status: 'pending'
    },
    {
      id: 'testing',
      name: 'Running Initial Tests',
      description: 'Verifying agent configuration and connectivity',
      status: 'pending'
    }
  ]);
  const [createdAgentId, setCreatedAgentId] = useState<string | null>(null);
  const [optimizedConfig, setOptimizedConfig] = useState<OptimizedAgentConfig | null>(null);
  const [progress, setProgress] = useState(0);

  const updateStepStatus = (stepId: string, status: CreationStep['status'], errorMessage?: string) => {
    setCreationSteps(prev => prev.map(step =>
      step.id === stepId
        ? { ...step, status, errorMessage }
        : step
    ));
  };

  const createAgent = async () => {
    setIsCreating(true);
    setProgress(0);

    try {
      // Step 1: OpenAI Configuration Optimization
      updateStepStatus('optimization', 'in-progress');
      setProgress(10);

      // Bundle all wizard data for OpenAI optimization
      const wizardData = bundleAgentWizardData(
        agentData,
        agentData.selectedTemplate,
        agentData.selectedVoice,
        {
          personalityTraits: agentData.personalityTraits,
          communicationStyle: agentData.communicationStyle,
          customInstructions: agentData.customInstructions,
          selectedKnowledgeBases: agentData.knowledgeBaseSelections,
          trainingExamples: agentData.trainingExamples,
          organizationContext: "Insurance company customer service",
          useCase: "Voice-based customer support and assistance",
          targetAudience: "Insurance customers and prospects"
        }
      );

      console.log('Sending wizard data to OpenAI for optimization:', wizardData);

      const optimizationResponse = await fetch('/api/openai/generate-agent-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(wizardData)
      });

      let currentOptimizedConfig: OptimizedAgentConfig;

      if (!optimizationResponse.ok) {
        const error = await optimizationResponse.json();
        console.warn('OpenAI optimization failed, falling back to original configuration:', error);

        // Fall back to original configuration if OpenAI fails
        currentOptimizedConfig = {
          llm: {
            general_prompt: agentData.behavior,
            begin_message: agentData.selectedTemplate?.beginMessage ||
              `Hello! I'm ${agentData.name}. How can I help you today?`,
            model: 'gpt-4o-mini'
          },
          agent: {
            agent_name: agentData.name,
            voice_id: agentData.voiceId,
            language: 'en-US',
            response_engine: {
              type: 'retell-llm',
              llm_id: '' // Will be populated after LLM creation
            }
          },
          metadata: {
            optimization_notes: 'Fallback configuration used due to OpenAI optimization failure',
            suggested_improvements: ['Consider reviewing OpenAI API configuration'],
            confidence_score: 0.7
          }
        };

        setOptimizedConfig(currentOptimizedConfig);
        updateStepStatus('optimization', 'completed', 'Used fallback configuration');
      } else {
        const optimizationResult = await optimizationResponse.json();
        console.log('OpenAI optimization result:', optimizationResult);

        currentOptimizedConfig = optimizationResult.optimizedConfig;
        setOptimizedConfig(currentOptimizedConfig);
        updateStepStatus('optimization', 'completed');

        // Show optimization insights to user
        if (currentOptimizedConfig.metadata.optimization_notes) {
          toast({
            title: "Configuration Optimized!",
            description: currentOptimizedConfig.metadata.optimization_notes,
            duration: 3000,
          });
        }
      }

      setProgress(30);

      // Step 2: Create LLM with optimized configuration
      updateStepStatus('llm', 'in-progress');

      const llmConfig = currentOptimizedConfig.llm;

      // Convert tools to RetellAI format
      const retellTools = agentData.tools?.filter(tool => tool.enabled).map(tool => {
        const retellTool: any = {
          type: tool.type,
          name: tool.name,
          description: tool.description
        };

        // Add tool-specific configurations
        if (tool.type === 'transfer_call' && tool.transfer_destination) {
          retellTool.transfer_destination = tool.transfer_destination;
        } else if (tool.type === 'press_digit' && tool.digit) {
          retellTool.digit = tool.digit;
        } else if (tool.type === 'custom' && tool.url) {
          retellTool.url = tool.url;
          retellTool.speak_after_execution = tool.speak_after_execution || false;
          retellTool.speak_during_execution = tool.speak_during_execution || false;
        } else if ((tool.type === 'check_availability_cal' || tool.type === 'book_appointment_cal') && tool.calendar_url) {
          retellTool.calendar_url = tool.calendar_url;
          retellTool.cal_api_key = tool.cal_api_key;
          retellTool.event_type_id = tool.event_type_id;
        }

        return retellTool;
      }) || [];

      const llmResponse = await fetch('/api/retell/llms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...llmConfig,
          general_tools: retellTools
        })
      });

      if (!llmResponse.ok) {
        const error = await llmResponse.json();
        throw new Error(error.error || 'Failed to create LLM');
      }

      const llmData = await llmResponse.json();
      updateStepStatus('llm', 'completed');
      setProgress(55);

      // Step 3: Create Agent with optimized configuration
      updateStepStatus('agent', 'in-progress');

      const agentConfig = currentOptimizedConfig.agent;

      // Prepare agent configuration based on agent type
      const agentPayload: any = {
        ...agentConfig,
        general_prompt: llmConfig.general_prompt,
        begin_message: llmConfig.begin_message,
        agent_type: agentData.agentType || 'voice_call'
      };

      // For chat-only agents, voice configuration is optional
      if (agentData.agentType === 'chat') {
        // Chat agents don't require voice_id, but we'll keep it for potential multi-modal use
        agentPayload.voice_id = agentData.voiceId || 'openai-alloy';
      }

      const agentResponse = await fetch('/api/retell/agents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(agentPayload)
      });

      if (!agentResponse.ok) {
        const error = await agentResponse.json();
        throw new Error(error.error || 'Failed to create agent');
      }

      const agentResponseData = await agentResponse.json();
      setCreatedAgentId(agentResponseData.agent_id);
      updateStepStatus('agent', 'completed');
      setProgress(75);

      // Step 4: Add to Organization
      updateStepStatus('organization', 'in-progress');

      // Get current user's organization ID
      const currentOrgResponse = await fetch('/api/organization/current');
      if (!currentOrgResponse.ok) {
        throw new Error('Failed to get current organization');
      }
      const currentOrg = await currentOrgResponse.json();

      const orgResponse = await fetch('/api/organization/agents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_id: agentResponseData.agent_id,
          name: agentData.name,
          type: 'inbound_voice',
          agent_type: agentData.agentType || 'voice_call',
          description: agentData.description,
          voice: agentData.selectedVoice?.voice_name || 'Unknown',
          organization_id: currentOrg.organization_id
        })
      });

      if (!orgResponse.ok) {
        const orgError = await orgResponse.json();
        console.warn('Failed to add agent to organization:', orgError);
        // Don't fail the entire process if org registration fails
        updateStepStatus('organization', 'completed', 'Agent created but not added to organization');
      } else {
        updateStepStatus('organization', 'completed');
      }
      setProgress(90);

      // Step 5: Testing
      updateStepStatus('testing', 'in-progress');

      // Simple connectivity test
      const testResponse = await fetch(`/api/retell/agents/${agentResponseData.agent_id}`);
      if (testResponse.ok) {
        updateStepStatus('testing', 'completed');
      } else {
        updateStepStatus('testing', 'error', 'Agent created but connectivity test failed');
      }

      setProgress(100);

      // Success!
      toast({
        title: "Agent Created Successfully!",
        description: `${agentData.name} is ready to start helping your customers.`,
        duration: 5000,
      });

      setTimeout(() => {
        onSuccess(agentResponseData.agent_id);
      }, 1500);

    } catch (error) {
      console.error('Error creating agent:', error);

      // Update the current step as error
      const currentStep = creationSteps.find(step => step.status === 'in-progress');
      if (currentStep) {
        updateStepStatus(currentStep.id, 'error', error instanceof Error ? error.message : 'Unknown error');
      }

      toast({
        title: "Failed to Create Agent",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsCreating(false);
    }
  };

  const getStepIcon = (status: CreationStep['status'], stepId?: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'in-progress':
        if (stepId === 'optimization') {
          return <Brain className="h-5 w-5 text-blue-500 animate-pulse" />;
        }
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        if (stepId === 'optimization') {
          return <Sparkles className="h-5 w-5 text-gray-400" />;
        }
        return <div className="h-5 w-5 rounded-full border-2 border-gray-400" />;
    }
  };

  const allStepsCompleted = creationSteps.every(step =>
    step.status === 'completed' || step.status === 'error'
  );

  const hasErrors = creationSteps.some(step => step.status === 'error');

  return (
    <div className="relative z-10 max-w-4xl mx-auto py-8">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-white mb-2">Creating Your AI Agent</h3>
        <p className="text-gray-300 mb-6">
          We're setting up your agent with RetellAI. This process takes about 30-60 seconds.
        </p>

        {/* Agent Summary */}
        <Card className="bg-gray-900 border-gray-800 mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              {agentData.selectedTemplate?.icon && (
                <span className="text-xl mr-2">{agentData.selectedTemplate.icon}</span>
              )}
              {agentData.name}
            </CardTitle>
            <CardDescription className="text-gray-300">
              {agentData.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <div className="text-sm text-gray-400">Template</div>
                <div className="text-white">
                  {agentData.selectedTemplate?.name || 'Custom Build'}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-400">Agent Type</div>
                <div className="text-white">
                  {agentData.agentType?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Voice Call'}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-400">Voice ID</div>
                <div className="text-white font-mono text-sm">{agentData.voiceId}</div>
              </div>
              <div>
                <div className="text-sm text-gray-400">Model</div>
                <div className="text-white">GPT-4o Mini</div>
              </div>
              <div>
                <div className="text-sm text-gray-400">Tools</div>
                <div className="text-white">
                  {agentData.tools?.filter(tool => tool.enabled).length || 0} enabled
                </div>
              </div>
            </div>
            {agentData.tools && agentData.tools.filter(tool => tool.enabled).length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-700">
                <div className="text-sm text-gray-400 mb-2">Enabled Tools:</div>
                <div className="flex flex-wrap gap-2">
                  {agentData.tools.filter(tool => tool.enabled).map((tool, index) => (
                    <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                      {tool.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Optimization Results */}
        {optimizedConfig && (
          <Card className="bg-blue-900/20 border-blue-800 mb-6">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Sparkles className="h-5 w-5 mr-2" />
                AI Optimization Results
              </CardTitle>
              <CardDescription className="text-gray-300">
                Your agent configuration has been optimized for better performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-400 mb-1">Optimization Notes</div>
                  <div className="text-white text-sm">
                    {optimizedConfig.metadata.optimization_notes}
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-400 mb-1">Confidence Score</div>
                  <div className="flex items-center space-x-2">
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${optimizedConfig.metadata.confidence_score * 100}%` }}
                      />
                    </div>
                    <span className="text-white text-sm">
                      {Math.round(optimizedConfig.metadata.confidence_score * 100)}%
                    </span>
                  </div>
                </div>

                {optimizedConfig.metadata.suggested_improvements.length > 0 && (
                  <div>
                    <div className="text-sm text-gray-400 mb-2">Suggested Improvements</div>
                    <div className="space-y-1">
                      {optimizedConfig.metadata.suggested_improvements.map((improvement, index) => (
                        <div key={index} className="text-sm text-gray-300 flex items-start">
                          <span className="text-blue-400 mr-2">•</span>
                          {improvement}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-400 mb-2">
            <span>Creation Progress</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Creation Steps */}
        <Card className="bg-gray-900 border-gray-800 mb-6">
          <CardHeader>
            <CardTitle className="text-white">Creation Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {creationSteps.map((step, index) => (
                <div key={step.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getStepIcon(step.status, step.id)}
                  </div>
                  <div className="flex-1">
                    <div className="text-white font-medium">{step.name}</div>
                    <div className="text-gray-400 text-sm">{step.description}</div>
                    {step.errorMessage && (
                      <div className="text-red-400 text-sm mt-1">{step.errorMessage}</div>
                    )}
                  </div>
                  <div className="flex-shrink-0">
                    {step.status === 'completed' && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Done
                      </Badge>
                    )}
                    {step.status === 'in-progress' && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        Working...
                      </Badge>
                    )}
                    {step.status === 'error' && (
                      <Badge variant="secondary" className="bg-red-100 text-red-800">
                        Error
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Success State */}
        {allStepsCompleted && !hasErrors && createdAgentId && (
          <Card className="bg-green-900/20 border-green-800">
            <CardContent className="p-6 text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-white mb-2">Agent Created Successfully!</h4>
              <p className="text-gray-300 mb-4">
                Your AI agent is now ready to start helping customers. You can test it, assign phone numbers,
                or integrate it into your existing systems.
              </p>
              <div className="flex justify-center space-x-3">
                <Button variant="outline" className="border-green-700 text-green-400 hover:bg-green-800">
                  <Play className="h-4 w-4 mr-2" />
                  Test Agent
                </Button>
                <Button variant="outline" className="border-green-700 text-green-400 hover:bg-green-800">
                  <Phone className="h-4 w-4 mr-2" />
                  Assign Phone Number
                </Button>
                <Button variant="outline" className="border-green-700 text-green-400 hover:bg-green-800">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-gray-700 text-white hover:bg-gray-800"
          disabled={isCreating}
        >
          Back
        </Button>

        {!isCreating && !allStepsCompleted && (
          <Button
            onClick={createAgent}
            className="bg-green-600 hover:bg-green-700 text-white px-8"
          >
            Create My AI Agent
          </Button>
        )}

        {isCreating && (
          <Button disabled className="bg-gray-600 text-white px-8">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Creating Agent...
          </Button>
        )}
      </div>
    </div>
  );
}
