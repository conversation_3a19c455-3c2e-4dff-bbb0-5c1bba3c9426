"use client"

import { Document, Page, Text, View, StyleSheet, Link, Image } from '@react-pdf/renderer'
import { Call } from '@/types/calls'

// Professional styles with modern design
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
    fontSize: 10,
    lineHeight: 1.4
  },

  // Header Styles
  headerContainer: {
    backgroundColor: '#1e40af',
    padding: 20,
    marginBottom: 25,
    borderRadius: 8,
    color: '#ffffff'
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15
  },
  headerLogo: {
    width: 100,
    height: 32,
    objectFit: 'contain'
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Helvetica-Bold',
    color: '#ffffff'
  },
  headerSubtitle: {
    fontSize: 12,
    color: '#e0e7ff',
    marginTop: 5
  },
  headerMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingTop: 10,
    borderTop: '1pt solid #3b82f6'
  },
  headerMetaItem: {
    fontSize: 10,
    color: '#e0e7ff'
  },

  // Executive Summary Card
  executiveSummary: {
    backgroundColor: '#f8fafc',
    padding: 15,
    marginBottom: 20,
    borderRadius: 6,
    border: '1pt solid #e2e8f0'
  },
  summaryTitle: {
    fontSize: 14,
    fontFamily: 'Helvetica-Bold',
    color: '#1e40af',
    marginBottom: 10
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  summaryCard: {
    backgroundColor: '#ffffff',
    padding: 10,
    borderRadius: 4,
    border: '1pt solid #e2e8f0',
    width: '23%',
    alignItems: 'center'
  },
  summaryValue: {
    fontSize: 16,
    fontFamily: 'Helvetica-Bold',
    color: '#1e40af',
    marginBottom: 2
  },
  summaryLabel: {
    fontSize: 8,
    color: '#64748b',
    textAlign: 'center'
  },

  // Section Styles
  section: {
    marginBottom: 18,
    backgroundColor: '#ffffff',
    borderRadius: 6,
    border: '1pt solid #e2e8f0'
  },
  sectionHeader: {
    backgroundColor: '#f8fafc',
    padding: 12,
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
    borderBottom: '1pt solid #e2e8f0'
  },
  sectionTitle: {
    fontSize: 12,
    fontFamily: 'Helvetica-Bold',
    color: '#1e40af',
    marginBottom: 0
  },
  sectionContent: {
    padding: 15
  },

  // Data Grid Styles
  dataGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'
  },
  dataItem: {
    width: '48%',
    marginBottom: 8,
    flexDirection: 'row'
  },
  dataLabel: {
    width: 120,
    fontSize: 9,
    color: '#64748b',
    fontFamily: 'Helvetica-Bold'
  },
  dataValue: {
    flex: 1,
    fontSize: 9,
    color: '#1e293b',
    fontFamily: 'Helvetica'
  },

  // Status Indicators
  statusSuccess: {
    backgroundColor: '#dcfce7',
    color: '#166534',
    padding: '3 8',
    borderRadius: 12,
    fontSize: 8,
    fontFamily: 'Helvetica-Bold'
  },
  statusError: {
    backgroundColor: '#fef2f2',
    color: '#dc2626',
    padding: '3 8',
    borderRadius: 12,
    fontSize: 8,
    fontFamily: 'Helvetica-Bold'
  },
  statusNeutral: {
    backgroundColor: '#f1f5f9',
    color: '#475569',
    padding: '3 8',
    borderRadius: 12,
    fontSize: 8,
    fontFamily: 'Helvetica-Bold'
  },

  // Transcript Styles
  transcriptContainer: {
    backgroundColor: '#f8fafc',
    borderRadius: 6,
    padding: 12,
    marginTop: 10
  },
  transcriptBlock: {
    marginBottom: 10,
    padding: 8,
    borderRadius: 4,
    borderLeft: '3pt solid'
  },
  transcriptAgent: {
    backgroundColor: '#eff6ff',
    borderLeftColor: '#3b82f6'
  },
  transcriptCustomer: {
    backgroundColor: '#f0fdf4',
    borderLeftColor: '#10b981'
  },
  speakerLabel: {
    fontSize: 9,
    fontFamily: 'Helvetica-Bold',
    marginBottom: 4
  },
  speakerText: {
    fontSize: 9,
    lineHeight: 1.4,
    color: '#374151'
  },

  // Performance Metrics
  metricsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15
  },
  metricCard: {
    width: '32%',
    backgroundColor: '#f8fafc',
    padding: 10,
    borderRadius: 4,
    border: '1pt solid #e2e8f0',
    alignItems: 'center'
  },
  metricValue: {
    fontSize: 14,
    fontFamily: 'Helvetica-Bold',
    color: '#1e40af',
    marginBottom: 2
  },
  metricLabel: {
    fontSize: 8,
    color: '#64748b',
    textAlign: 'center'
  },

  // Footer
  footer: {
    position: 'absolute',
    bottom: 20,
    left: 30,
    right: 30,
    fontSize: 8,
    color: '#94a3b8',
    borderTop: '1pt solid #e2e8f0',
    paddingTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  footerLogo: {
    width: 20,
    height: 20,
    objectFit: 'contain'
  },
  footerText: {
    fontSize: 8,
    color: '#64748b'
  },

  // Call Summary Box
  callSummaryBox: {
    backgroundColor: '#fefce8',
    border: '1pt solid #fbbf24',
    borderRadius: 6,
    padding: 12,
    marginBottom: 15
  },
  callSummaryText: {
    fontSize: 10,
    lineHeight: 1.5,
    color: '#92400e',
    fontStyle: 'italic'
  }
})

interface CallReportProps {
  call: Call
}

export function CallReport({ call }: CallReportProps) {
  // Utility functions
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    }
    return `${minutes}m ${secs}s`
  }

  const formatCost = (cost: number) => `$${(cost / 100).toFixed(3)}`

  const getCustomerName = () => {
    if (call.dynamic_variables) {
      if (typeof call.dynamic_variables === 'object') {
        return call.dynamic_variables.customer_name || call.customer_name || 'Unknown'
      }
      if (typeof call.dynamic_variables === 'string') {
        try {
          const parsed = JSON.parse(call.dynamic_variables)
          return parsed.customer_name || call.customer_name || 'Unknown'
        } catch {
          return call.customer_name || 'Unknown'
        }
      }
    }
    return call.customer_name || 'Unknown'
  }

  const getStatusStyle = (success: boolean | null) => {
    if (success === true) return styles.statusSuccess
    if (success === false) return styles.statusError
    return styles.statusNeutral
  }

  const getSentimentStyle = (sentiment: string) => {
    const lower = sentiment?.toLowerCase()
    if (lower?.includes('positive')) return styles.statusSuccess
    if (lower?.includes('negative')) return styles.statusError
    return styles.statusNeutral
  }

  const formatTranscript = (transcript: string) => {
    if (!transcript) return <Text style={styles.speakerText}>No transcript available</Text>

    const lines = transcript.split('\n').filter(line => line.trim())
    const blocks: Array<{ speaker: 'agent' | 'customer'; text: string }> = []

    lines.forEach((line) => {
      const trimmedLine = line.trim()
      if (trimmedLine.toLowerCase().startsWith('agent:')) {
        const content = line.split(':').slice(1).join(':').trim()
        if (content) blocks.push({ speaker: 'agent', text: content })
      } else if (trimmedLine.toLowerCase().startsWith('user:') || trimmedLine.toLowerCase().startsWith('customer:')) {
        const content = line.split(':').slice(1).join(':').trim()
        if (content) blocks.push({ speaker: 'customer', text: content })
      } else if (blocks.length > 0) {
        // Continuation of previous speaker
        blocks[blocks.length - 1].text += ' ' + trimmedLine
      }
    })

    return (
      <View style={styles.transcriptContainer}>
        {blocks.map((block, index) => (
          <View
            key={index}
            style={[
              styles.transcriptBlock,
              block.speaker === 'agent' ? styles.transcriptAgent : styles.transcriptCustomer
            ]}
          >
            <Text style={styles.speakerLabel}>
              {block.speaker === 'agent' ? '🤖 AI Agent' : '👤 Customer'}
            </Text>
            <Text style={styles.speakerText}>{block.text}</Text>
          </View>
        ))}
      </View>
    )
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Professional Header */}
        <View style={styles.headerContainer}>
          <View style={styles.headerTop}>
            <Image
              src={process.env.NODE_ENV === 'development'
                ? 'http://localhost:3001/valabs_logo.png'
                : '/valabs_logo.png'}
              style={styles.headerLogo}
            />
            <Text style={styles.headerTitle}>Call Analytics Report</Text>
          </View>
          <Text style={styles.headerSubtitle}>
            Comprehensive analysis and insights for call ID: {call.call_id}
          </Text>
          <View style={styles.headerMeta}>
            <Text style={styles.headerMetaItem}>
              Generated: {new Date().toLocaleDateString('en-US', {
                year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
              })}
            </Text>
            <Text style={styles.headerMetaItem}>
              Agent: {call.agent_name}
            </Text>
            <Text style={styles.headerMetaItem}>
              Customer: {getCustomerName()}
            </Text>
          </View>
        </View>

        {/* Executive Summary */}
        <View style={styles.executiveSummary}>
          <Text style={styles.summaryTitle}>Executive Summary</Text>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryCard}>
              <Text style={styles.summaryValue}>{formatDuration(call.duration_seconds)}</Text>
              <Text style={styles.summaryLabel}>Call Duration</Text>
            </View>
            <View style={styles.summaryCard}>
              <Text style={[styles.summaryValue, getStatusStyle(call.call_successful)]}>
                {call.call_successful ? '✓ Success' : '✗ Failed'}
              </Text>
              <Text style={styles.summaryLabel}>Call Outcome</Text>
            </View>
            <View style={styles.summaryCard}>
              <Text style={[styles.summaryValue, getSentimentStyle(call.user_sentiment)]}>
                {call.user_sentiment || 'N/A'}
              </Text>
              <Text style={styles.summaryLabel}>Customer Sentiment</Text>
            </View>
            <View style={styles.summaryCard}>
              <Text style={styles.summaryValue}>{formatCost(call.cost)}</Text>
              <Text style={styles.summaryLabel}>Call Cost</Text>
            </View>
          </View>
        </View>

        {/* Call Details Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>📞 Call Details</Text>
          </View>
          <View style={styles.sectionContent}>
            <View style={styles.dataGrid}>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Call Date:</Text>
                <Text style={styles.dataValue}>{formatDate(call.start_timestamp)}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Duration:</Text>
                <Text style={styles.dataValue}>{formatDuration(call.duration_seconds)}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Call Cost:</Text>
                <Text style={styles.dataValue}>{formatCost(call.cost)}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Call Status:</Text>
                <Text style={[styles.dataValue, getStatusStyle(call.call_successful)]}>
                  {call.call_successful ? 'Successful' : 'Failed'}
                </Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Disconnection:</Text>
                <Text style={styles.dataValue}>{call.disconnection_reason || 'N/A'}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Latency:</Text>
                <Text style={styles.dataValue}>
                  {call.end_to_end_latency ? `${call.end_to_end_latency}ms` : 'N/A'}
                </Text>
              </View>
            </View>
            {(call.recording_url || call.audio_url) && (
              <View style={{ marginTop: 10 }}>
                <Text style={styles.dataLabel}>Recording URL:</Text>
                <Link src={call.recording_url || call.audio_url} style={{ fontSize: 8, color: '#3b82f6' }}>
                  {call.recording_url || call.audio_url}
                </Link>
              </View>
            )}
          </View>
        </View>

        {/* Participants Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>👥 Participants</Text>
          </View>
          <View style={styles.sectionContent}>
            <View style={styles.dataGrid}>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Customer Name:</Text>
                <Text style={styles.dataValue}>{getCustomerName()}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Customer Phone:</Text>
                <Text style={styles.dataValue}>{call.customer_phone || 'N/A'}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Agent Name:</Text>
                <Text style={styles.dataValue}>{call.agent_name}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Agent Phone:</Text>
                <Text style={styles.dataValue}>{call.agent_phone || 'N/A'}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Call Summary Section */}
        {call.call_summary && (
          <View style={styles.callSummaryBox}>
            <Text style={styles.summaryTitle}>📋 Call Summary</Text>
            <Text style={styles.callSummaryText}>{call.call_summary}</Text>
          </View>
        )}

        {/* Performance Analysis */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>📊 Performance Analysis</Text>
          </View>
          <View style={styles.sectionContent}>
            <View style={styles.metricsGrid}>
              <View style={styles.metricCard}>
                <Text style={[styles.metricValue, getSentimentStyle(call.user_sentiment)]}>
                  {call.user_sentiment || 'N/A'}
                </Text>
                <Text style={styles.metricLabel}>Customer Sentiment</Text>
              </View>
              <View style={styles.metricCard}>
                <Text style={[styles.metricValue, getStatusStyle(call.call_successful)]}>
                  {call.call_successful ? 'Success' : 'Failed'}
                </Text>
                <Text style={styles.metricLabel}>Call Outcome</Text>
              </View>
              <View style={styles.metricCard}>
                <Text style={styles.metricValue}>
                  {call.end_to_end_latency ? `${call.end_to_end_latency}ms` : 'N/A'}
                </Text>
                <Text style={styles.metricLabel}>Response Latency</Text>
              </View>
            </View>

            {/* Additional Analysis Data */}
            <View style={styles.dataGrid}>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Task Completion:</Text>
                <Text style={styles.dataValue}>{(call as any).task_completion || 'N/A'}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Completion Rating:</Text>
                <Text style={styles.dataValue}>{(call as any).completion_rating || 'N/A'}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Disconnection Reason:</Text>
                <Text style={styles.dataValue}>{call.disconnection_reason || 'N/A'}</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Call Type:</Text>
                <Text style={styles.dataValue}>Voice Call</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Conversation Transcript */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>💬 Conversation Transcript</Text>
          </View>
          <View style={styles.sectionContent}>
            {formatTranscript(call.transcript)}
          </View>
        </View>

        {/* Professional Footer */}
        <View style={styles.footer}>
          <Image
            src={process.env.NODE_ENV === 'development'
              ? 'http://localhost:3001/valabs_logo.png'
              : '/valabs_logo.png'}
            style={styles.footerLogo}
          />
          <Text style={styles.footerText}>
            Virtual Assistant Labs (VAL) • Call Analytics Report • Confidential
          </Text>
          <Text style={styles.footerText}>
            Page 1 of 1
          </Text>
        </View>
      </Page>
    </Document>
  )
}