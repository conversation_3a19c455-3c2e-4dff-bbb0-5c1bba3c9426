"use client";

import React, { useEffect, useState } from 'react';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  X
} from "lucide-react";
import { useAgentSync } from '@/hooks/useAgentSync';

interface AgentSyncNotificationProps {
  onSyncClick?: () => void;
  dismissible?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export function AgentSyncNotification({
  onSyncClick,
  dismissible = true,
  autoHide = false,
  autoHideDelay = 5000
}: AgentSyncNotificationProps) {
  const { isSyncNeeded, syncHealth, syncStatus, error } = useAgentSync();
  const [dismissed, setDismissed] = useState(false);

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && (isSyncNeeded || error)) {
      const timer = setTimeout(() => {
        setDismissed(true);
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, isSyncNeeded, error]);

  // Reset dismissed state when sync status changes
  useEffect(() => {
    setDismissed(false);
  }, [isSyncNeeded, error]);

  if (dismissed) return null;

  // Show error notification
  if (error) {
    return (
      <Alert className="border-red-800 bg-red-900/20">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-red-400 flex items-center justify-between">
          <span>Sync Error: {error}</span>
          <div className="flex items-center gap-2">
            {onSyncClick && (
              <Button
                onClick={onSyncClick}
                variant="ghost"
                size="sm"
                className="text-red-400 hover:text-red-300"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            )}
            {dismissible && (
              <Button
                onClick={() => setDismissed(true)}
                variant="ghost"
                size="sm"
                className="text-red-400 hover:text-red-300 p-1"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Show sync needed notification
  if (isSyncNeeded) {
    return (
      <Alert className="border-yellow-800 bg-yellow-900/20">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-yellow-400 flex items-center justify-between">
          <span>
            Your agents are out of sync with VAL.
            {syncStatus && (
              <span className="ml-1">
                ({syncStatus.current_status.retell_agents} VAL vs {syncStatus.current_status.db_agents_active} local)
              </span>
            )}
          </span>
          <div className="flex items-center gap-2">
            {onSyncClick && (
              <Button
                onClick={onSyncClick}
                variant="ghost"
                size="sm"
                className="text-yellow-400 hover:text-yellow-300"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Sync Now
              </Button>
            )}
            {dismissible && (
              <Button
                onClick={() => setDismissed(true)}
                variant="ghost"
                size="sm"
                className="text-yellow-400 hover:text-yellow-300 p-1"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Show success notification when healthy
  if (syncHealth === 'healthy' && syncStatus?.last_sync) {
    return (
      <Alert className="border-green-800 bg-green-900/20">
        <CheckCircle className="h-4 w-4" />
        <AlertDescription className="text-green-400 flex items-center justify-between">
          <span>
            Agents are synchronized with VAL
            ({syncStatus.current_status.retell_agents} agents)
          </span>
          {dismissible && (
            <Button
              onClick={() => setDismissed(true)}
              variant="ghost"
              size="sm"
              className="text-green-400 hover:text-green-300 p-1"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return null;
}
