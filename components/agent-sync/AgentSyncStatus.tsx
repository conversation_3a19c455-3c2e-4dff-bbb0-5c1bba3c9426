"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Database,
  CheckCircle,
  AlertTriangle,
  Clock,
  ExternalLink,
  Info
} from "lucide-react";
import { useAgentSync } from '@/hooks/useAgentSync';
import { AgentSyncButton } from './AgentSyncButton';

interface AgentSyncStatusProps {
  onSyncComplete?: () => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function AgentSyncStatus({
  onSyncComplete,
  autoRefresh = true,
  refreshInterval = 30000
}: AgentSyncStatusProps) {
  const {
    loading,
    error,
    syncStatus,
    getSyncStatus,
    isSyncNeeded,
    timeSinceLastSync,
    syncHealth,
    clearError
  } = useAgentSync();

  // Load sync status on mount
  useEffect(() => {
    getSyncStatus();
  }, [getSyncStatus]);

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (!loading) {
        getSyncStatus();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [getSyncStatus, loading, autoRefresh, refreshInterval]);

  const getSyncHealthColor = () => {
    switch (syncHealth) {
      case 'healthy': return 'bg-green-500';
      case 'needs_sync': return 'bg-yellow-500';
      case 'warning': return 'bg-red-500';
      case 'stale': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getSyncHealthText = () => {
    switch (syncHealth) {
      case 'healthy': return 'In Sync';
      case 'needs_sync': return 'Sync Needed';
      case 'warning': return 'Warning';
      case 'stale': return 'Stale';
      case 'never_synced': return 'Never Synced';
      default: return 'Unknown';
    }
  };

  const getSyncHealthDescription = () => {
    switch (syncHealth) {
      case 'healthy': return 'Your agents are synchronized with VAL';
      case 'needs_sync': return 'There are differences between your local agents and VAL';
      case 'warning': return 'Last sync completed with errors';
      case 'stale': return 'Last sync was more than 24 hours ago';
      case 'never_synced': return 'No synchronization has been performed yet';
      default: return 'Unable to determine sync status';
    }
  };

  return (
    <div className="space-y-4">
      {/* Main Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Agent Synchronization
              </CardTitle>
              <CardDescription>
                {getSyncHealthDescription()}
              </CardDescription>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${getSyncHealthColor()}`} />
                <Badge variant="outline">{getSyncHealthText()}</Badge>
              </div>
              <AgentSyncButton
                onSyncComplete={onSyncComplete}
                showStatus={false}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4 border-red-800 bg-red-900/20">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-red-400">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {syncStatus && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {syncStatus.current_status.retell_agents}
                </div>
                <div className="text-sm text-gray-400">VAL Agents</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {syncStatus.current_status.db_agents_active}
                </div>
                <div className="text-sm text-gray-400">Active in DB</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-400">
                  {syncStatus.current_status.db_agents_inactive}
                </div>
                <div className="text-sm text-gray-400">Inactive in DB</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">
                  {isSyncNeeded ? 'Yes' : 'No'}
                </div>
                <div className="text-sm text-gray-400">Sync Needed</div>
              </div>
            </div>
          )}

          {timeSinceLastSync && (
            <div className="mt-4 flex items-center gap-2 text-sm text-gray-400">
              <Clock className="h-4 w-4" />
              Last synced {timeSinceLastSync}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Last Sync Summary */}
      {syncStatus?.last_sync_summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Last Sync Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-lg font-bold">
                  {syncStatus.last_sync_summary.total_retell_agents}
                </div>
                <div className="text-sm text-gray-400">VAL</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-400">
                  {syncStatus.last_sync_summary.added}
                </div>
                <div className="text-sm text-gray-400">Added</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-400">
                  {syncStatus.last_sync_summary.updated}
                </div>
                <div className="text-sm text-gray-400">Updated</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-400">
                  {syncStatus.last_sync_summary.removed}
                </div>
                <div className="text-sm text-gray-400">Removed</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-400">
                  {syncStatus.last_sync_summary.errors}
                </div>
                <div className="text-sm text-gray-400">Errors</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            How Sync Works
          </CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-gray-400 space-y-2">
          <p>• <strong>Add:</strong> Agents in VAL but not in your database will be added</p>
          <p>• <strong>Update:</strong> Existing agents will have their metadata refreshed</p>
          <p>• <strong>Remove:</strong> Agents deleted from VAL will be marked as inactive</p>
          <p>• <strong>Preview:</strong> Use the preview button to see changes before applying them</p>
        </CardContent>
      </Card>
    </div>
  );
}
