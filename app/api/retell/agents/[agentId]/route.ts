import { NextResponse } from 'next/server'
import { getAgentDetails, updateAgent, deleteAgent, getRetellLLM, listKnowledgeBases } from '@/lib/retell'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

// GET /api/retell/agents/[agentId] - Get comprehensive agent details
export async function GET(
  request: Request,
  { params }: { params: { agentId: string } }
) {
  try {
    // Fetch agent details from RetellAI
    const agent = await getAgentDetails(params.agentId)

    // Initialize additional data
    let llmDetails = null
    let knowledgeBases = []
    let callStats = { totalCalls: 0, totalMinutes: 0 }

    // Fetch LLM details if available
    if (agent.response_engine?.llm_id) {
      try {
        llmDetails = await getRetellLLM(agent.response_engine.llm_id)
      } catch (error) {
        console.warn('Failed to fetch LLM details:', error)
      }
    }

    // Fetch knowledge bases if LLM has them
    if (llmDetails?.knowledge_base_ids?.length > 0) {
      try {
        const allKnowledgeBases = await listKnowledgeBases()
        knowledgeBases = allKnowledgeBases.filter(kb =>
          llmDetails.knowledge_base_ids.includes(kb.knowledge_base_id)
        )
      } catch (error) {
        console.warn('Failed to fetch knowledge bases:', error)
      }
    }

    // Fetch call statistics from database
    try {
      const { data: callData, error: callError } = await supabaseServiceRole
        .from('calls')
        .select('duration_ms')
        .eq('retell_agent_id', params.agentId)
        .eq('call_status', 'ended')

      if (!callError && callData) {
        callStats.totalCalls = callData.length
        callStats.totalMinutes = callData.reduce((sum, call) =>
          sum + (call.duration_ms / (1000 * 60)), 0
        )
      }
    } catch (error) {
      console.warn('Failed to fetch call statistics:', error)
    }

    // Return comprehensive agent data
    return NextResponse.json({
      ...agent,
      llm_details: llmDetails,
      knowledge_bases: knowledgeBases,
      call_statistics: callStats
    })
  } catch (error) {
    console.error('Error fetching agent:', error)
    return NextResponse.json(
      { error: 'Failed to fetch agent' },
      { status: 500 }
    )
  }
}

// PUT /api/retell/agents/[agentId] - Update agent
export async function PUT(
  request: Request,
  { params }: { params: { agentId: string } }
) {
  try {
    const body = await request.json()
    const updatedAgent = await updateAgent(params.agentId, body)
    return NextResponse.json(updatedAgent)
  } catch (error) {
    console.error('Error updating agent:', error)
    return NextResponse.json(
      { error: 'Failed to update agent' },
      { status: 500 }
    )
  }
}

// DELETE /api/retell/agents/[agentId] - Delete agent
export async function DELETE(
  request: Request,
  { params }: { params: { agentId: string } }
) {
  try {
    await deleteAgent(params.agentId)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting agent:', error)
    return NextResponse.json(
      { error: 'Failed to delete agent' },
      { status: 500 }
    )
  }
}
