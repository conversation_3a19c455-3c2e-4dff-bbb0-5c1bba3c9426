import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Use service role key for webhook to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!, // Make sure to add this to your .env
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: Request) {
  try {
    // Verify webhook signature if RetellAI provides one
    // const signature = request.headers.get('x-retell-signature')

    const payload = await request.json()

    console.log('Received webhook event:', payload.event, 'for call:', payload.call?.call_id)

    // Handle different event types
    if (payload.event === 'call_started') {
      // Just log call started, don't store yet
      console.log('Call started:', payload.call?.call_id)
      return NextResponse.json({ success: true, message: 'Call started logged' })
    }

    if (payload.event === 'call_ended' || payload.event === 'call_analyzed') {
      // Call the database function we created in our migration
      const { data, error } = await supabase
        .rpc('handle_call_webhook', {
          payload
        })

      if (error) {
        console.error('Error processing webhook:', error)
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        )
      }

      return NextResponse.json({ success: true, call_id: data })
    }

    // Unsupported event type
    console.log('Unsupported event type:', payload.event)
    return NextResponse.json({
      success: true,
      message: `Event ${payload.event} received but not processed`
    })

  } catch (err) {
    console.error('Webhook error:', err)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}