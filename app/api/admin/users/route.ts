import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { supabaseServiceRole } from '@/lib/supabase/service-role'

export const dynamic = 'force-dynamic';

// GET /api/admin/users - List all users in the admin's organization
export async function GET() {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Server component limitation
            }
          },
        },
      }
    )

    // Get the current admin user
    const { data: { user: adminUser }, error: adminError } = await supabase.auth.getUser()
    if (adminError || !adminUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify admin privileges
    const { data: adminMembership } = await supabase
      .from('organization_members')
      .select('role, organization_id, organizations(name)')
      .eq('user_id', adminUser.id)
      .eq('role', 'admin')
      .single()

    if (!adminMembership) {
      return NextResponse.json(
        { error: 'Admin privileges required' },
        { status: 403 }
      )
    }

    // Get all organization members
    const { data: members, error: membersError } = await supabase
      .from('organization_members')
      .select(`
        user_id,
        role,
        joined_at
      `)
      .eq('organization_id', adminMembership.organization_id)
      .order('joined_at', { ascending: false })

    if (membersError) {
      console.error('Error fetching organization members:', membersError)
      return NextResponse.json(
        { error: 'Failed to fetch organization members' },
        { status: 500 }
      )
    }

    // Get user details for each member using auth admin API
    const formattedUsers = []
    for (const member of members || []) {
      try {
        const { data: userData, error: userError } = await supabaseServiceRole.auth.admin.getUserById(member.user_id)

        if (!userError && userData?.user) {
          formattedUsers.push({
            id: member.user_id,
            email: userData.user.email,
            role: member.role,
            joinedAt: member.joined_at,
            createdAt: userData.user.created_at,
            lastSignInAt: userData.user.last_sign_in_at,
            isCurrentUser: member.user_id === adminUser.id
          })
        }
      } catch (error) {
        console.error(`Error fetching user ${member.user_id}:`, error)
        // Continue with other users even if one fails
      }
    }

    return NextResponse.json({
      users: formattedUsers,
      organization: {
        id: adminMembership.organization_id,
        name: adminMembership.organizations?.name
      },
      totalUsers: formattedUsers.length
    })

  } catch (error) {
    console.error('List users error:', error)
    return NextResponse.json(
      { error: 'Failed to list users' },
      { status: 500 }
    )
  }
}
