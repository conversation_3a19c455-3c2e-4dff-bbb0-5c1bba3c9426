"use client";

import { ImpersonationProvider } from '@/contexts/ImpersonationContext'

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ImpersonationProvider>
      <div className="min-h-screen bg-background">
        <div className="border-b border-border bg-card">
          <div className="max-w-7xl mx-auto px-8 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-xl font-semibold">VALabs Admin Panel</h1>
                <p className="text-sm text-muted-foreground">
                  System administration and organization management
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                Admin Access
              </div>
            </div>
          </div>
        </div>
        {children}
      </div>
    </ImpersonationProvider>
  )
}
