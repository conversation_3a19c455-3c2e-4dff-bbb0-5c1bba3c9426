"use client"

import { useState, useEffect, useRef, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { Card } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { supabase } from "@/lib/supabase"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import {
  ArrowUpDown, X, Copy, Play, Download,
  CheckCircle2,
  Phone,
  UserCircle2,
  PhoneOff,
  Timer,
  HelpCircle, Pause, FileText
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { CallReport } from '@/components/call-report'
import { TimeframeSelector } from "@/components/timeframe-selector"
import { AgentSelector } from "@/components/agent-selector"
import { useTimeframe } from "@/contexts/TimeframeContext"
import { useAgent } from "@/contexts/AgentContext"

interface DynamicVariables {
  customer_name?: string;
  [key: string]: string | undefined;
}

interface Call {
  call_id: string
  customer_name: string
  agent_name: string
  start_timestamp: string
  duration_seconds: number
  user_sentiment: string
  call_successful: boolean
  cost: number
  call_summary: string
  transcript: string
  customer_phone?: string
  agent_phone?: string
  end_to_end_latency?: number
  disconnection_reason?: string
  audio_url?: string
  recording_url?: string
  dynamic_variables: DynamicVariables | string | null
}

// Add new interfaces for filters
interface Filters {
  agent: string;
  duration: string;
  sentiment: string;
  success: string;
}

// Add constants for filter values
const FILTER_VALUES = {
  ALL: {
    AGENTS: 'all_agents',
    DURATION: 'all_duration',
    SENTIMENT: 'all_sentiment',
    SUCCESS: 'all_success'
  }
} as const

const DURATION_RANGES = {
  SHORT: { label: 'Short (0-5 min)', min: 0, max: 300 },
  MEDIUM: { label: 'Medium (5-15 min)', min: 300, max: 900 },
  LONG: { label: 'Long (15+ min)', min: 900, max: Infinity }
} as const

type SortField = keyof Pick<Call, 'start_timestamp' | 'customer_name' | 'agent_name' | 'duration_seconds' | 'cost'>
type SortOrder = 'asc' | 'desc'

// Removed TIMEFRAME_OPTIONS - using global timeframe context

const generatePDF = async (call: Call) => {
  // Dynamically import react-pdf to avoid SSR issues
  const { pdf } = await import('@react-pdf/renderer')
  const blob = await pdf(<CallReport call={call} />).toBlob()
  return blob
}

// Add dynamic config export
export const dynamic = 'force-dynamic'

// Create a wrapper component for the search params logic
function CallHistoryContent(): JSX.Element {
  const searchParams = useSearchParams()
  const dateParam = searchParams.get('date')
  const { timeframe } = useTimeframe()
  const { selectedAgent } = useAgent()
  const [localTimeframe, setLocalTimeframe] = useState<string>(
    dateParam ? 'custom' : timeframe
  )
  const [calls, setCalls] = useState<Call[]>([])
  const [selectedCall, setSelectedCall] = useState<Call | null>(null)
  const [sortField, setSortField] = useState<SortField>('start_timestamp')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')

  // Update initial filter state to use ALL values
  const [filters, setFilters] = useState<Filters>({
    agent: FILTER_VALUES.ALL.AGENTS,
    duration: FILTER_VALUES.ALL.DURATION,
    sentiment: FILTER_VALUES.ALL.SENTIMENT,
    success: FILTER_VALUES.ALL.SUCCESS
  })

  // Add unique agents state
  const [uniqueAgents, setUniqueAgents] = useState<string[]>([])
  const [allAvailableAgents, setAllAvailableAgents] = useState<string[]>([])

  const audioRef = useRef<HTMLAudioElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [progress, setProgress] = useState(0)
  const { toast } = useToast()

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortOrder('asc')
    }
  }

  // Fetch all available agents from API (bypasses RLS)
  useEffect(() => {
    const fetchAllAgents = async () => {
      try {
        const response = await fetch('/api/agents')
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const agents = await response.json()
        const agentNames = agents.map((agent: any) => agent.name).filter(Boolean)
        setAllAvailableAgents(agentNames)
      } catch (err) {
        console.error('Error fetching all agents:', err)
      }
    }

    fetchAllAgents()
  }, [])

  // Sync local timeframe with global timeframe when not in custom mode
  useEffect(() => {
    if (!dateParam) {
      setLocalTimeframe(timeframe)
    }
  }, [timeframe, dateParam])

  useEffect(() => {
    const fetchCalls = async () => {
      let startDate: Date
      let endDate: Date
      const effectiveTimeframe = dateParam ? 'custom' : timeframe

      const currentDate = new Date()

      if (effectiveTimeframe === 'custom' && dateParam) {
        // If we have a specific date parameter, use it
        startDate = new Date(dateParam)
        startDate.setHours(0, 0, 0, 0)
        endDate = new Date(dateParam)
        endDate.setHours(23, 59, 59, 999)
      } else {
        // Otherwise use the timeframe logic
        if (effectiveTimeframe === "today") {
          startDate = new Date(currentDate)
          startDate.setHours(0, 0, 0, 0)
          endDate = new Date(currentDate)
          endDate.setHours(23, 59, 59, 999)
        } else if (effectiveTimeframe === "all") {
          // For "all time", set a very early start date
          startDate = new Date('2020-01-01')
          endDate = new Date(currentDate)
        } else {
          startDate = new Date(currentDate)
          startDate.setDate(startDate.getDate() - parseInt(effectiveTimeframe))
          endDate = new Date(currentDate)
        }
      }

      try {
        // Fetch calls with date filters from the actual calls table
        const { data, error } = await supabase
          .from('calls')
          .select(`
            call_id,
            start_timestamp,
            duration_seconds,
            user_sentiment,
            call_successful,
            cost,
            call_summary,
            transcript,
            dynamic_variables,
            recording_url,
            organization_agents!inner(name)
          `)
          .order('start_timestamp', { ascending: false })
          .gte('start_timestamp', startDate.toISOString())
          .lte('start_timestamp', endDate.toISOString())

        if (error) {
          console.error('Error fetching calls:', error)
          return
        }

        // Map the data to match the expected interface
        const mappedData = data?.map(call => ({
          call_id: call.call_id,
          customer_name: '', // Will be extracted from dynamic_variables by getCustomerName function
          agent_name: call.organization_agents?.name || 'Unknown Agent',
          start_timestamp: call.start_timestamp,
          duration_seconds: call.duration_seconds || 0,
          user_sentiment: call.user_sentiment || 'Unknown',
          call_successful: call.call_successful || false,
          cost: call.cost || 0,
          call_summary: call.call_summary || '',
          transcript: call.transcript || '',
          dynamic_variables: call.dynamic_variables,
          recording_url: call.recording_url,
          audio_url: call.recording_url || call.audio_url
        }))

        if (data) {
          // Update unique agents when data is fetched
          const agents = Array.from(new Set(mappedData?.map(call => call.agent_name))).filter(Boolean).sort()
          setUniqueAgents(agents)
          setCalls(mappedData || [])
        }
      } catch (err) {
        console.error('Error in fetchCalls:', err)
      }
    }

    fetchCalls()
  }, [timeframe, dateParam])

  // Update filter logic
  const filteredCalls = useMemo(() => {
    return calls.filter(call => {
      // Agent filter (use global selectedAgent)
      if (selectedAgent !== "all" && call.agent_name !== selectedAgent) {
        return false
      }

      // Duration filter
      if (filters.duration !== FILTER_VALUES.ALL.DURATION) {
        const duration = call.duration_seconds
        const range = DURATION_RANGES[filters.duration as keyof typeof DURATION_RANGES]
        if (duration < range.min || duration >= range.max) {
          return false
        }
      }

      // Sentiment filter
      if (filters.sentiment !== FILTER_VALUES.ALL.SENTIMENT && call.user_sentiment !== filters.sentiment) {
        return false
      }

      // Success filter
      if (filters.success !== FILTER_VALUES.ALL.SUCCESS) {
        const isSuccessful = filters.success === 'true'
        if (call.call_successful !== isSuccessful) {
          return false
        }
      }

      return true
    })
  }, [calls, selectedAgent, filters])

  const sortedCalls = useMemo(() => {
    return [...filteredCalls].sort((a, b) => {
      const multiplier = sortOrder === 'asc' ? 1 : -1

      switch (sortField) {
        case 'start_timestamp':
          return multiplier * (new Date(a.start_timestamp).getTime() - new Date(b.start_timestamp).getTime())
        case 'duration_seconds':
          return multiplier * (a.duration_seconds - b.duration_seconds)
        case 'cost':
          return multiplier * ((a.cost / 100) - (b.cost / 100))
        default:
          const aValue = a[sortField]?.toLowerCase() || ''
          const bValue = b[sortField]?.toLowerCase() || ''
          return multiplier * aValue.localeCompare(bValue)
      }
    })
  }, [filteredCalls, sortField, sortOrder])

  // Update reset filters function
  const resetFilters = () => {
    setFilters({
      agent: FILTER_VALUES.ALL.AGENTS, // Keep this for compatibility but it won't be used
      duration: FILTER_VALUES.ALL.DURATION,
      sentiment: FILTER_VALUES.ALL.SENTIMENT,
      success: FILTER_VALUES.ALL.SUCCESS
    })
  }

  // Update the helper function to format the transcript
  const formatTranscript = (transcript: string) => {
    let currentSpeaker: 'agent' | 'user' | null = null;

    return transcript.split('\n').map((line, index) => {
      if (!line.trim()) return null // Skip empty lines

      // Check if this line starts a new speaker
      if (line.toLowerCase().startsWith('agent:')) {
        currentSpeaker = 'agent'
      } else if (line.toLowerCase().startsWith('user:')) {
        currentSpeaker = 'user'
      }

      // If it's a continuation line (no speaker prefix), use the current speaker's color
      const isNewSpeaker = line.toLowerCase().includes(':')
      const [prefix, ...contentParts] = isNewSpeaker ? line.split(':') : ['', line]
      const content = isNewSpeaker ? contentParts.join(':') : line

      return (
        <div
          key={index}
          className={cn(
            "py-1",
            currentSpeaker === 'agent' && "text-blue-500 dark:text-blue-400",
            currentSpeaker === 'user' && "text-green-500 dark:text-green-400"
          )}
        >
          {isNewSpeaker ? (
            <>
              <span className="font-semibold">{prefix}:</span>
              <span>{content}</span>
            </>
          ) : (
            <span className="ml-[4.5em]">{content}</span>
          )}
        </div>
      )
    }).filter(Boolean)
  }

  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
      setProgress((audioRef.current.currentTime / audioRef.current.duration) * 100)
    }
  }

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
    }
  }

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current) {
      const bounds = e.currentTarget.getBoundingClientRect()
      const x = e.clientX - bounds.left
      const width = bounds.width
      const percentage = x / width
      const newTime = percentage * audioRef.current.duration
      audioRef.current.currentTime = newTime
      setProgress(percentage * 100)
    }
  }

  const handleDownload = async () => {
    const audioUrl = selectedCall?.recording_url || selectedCall?.audio_url

    if (audioUrl) {
      try {
        const response = await fetch(audioUrl, {
          mode: 'cors',
          credentials: 'include',
          headers: {
            'Access-Control-Allow-Origin': '*'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `call-${selectedCall.call_id}.wav`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } catch (err) {
        toast({
          title: "Download failed",
          description: `Could not download the audio file: ${err instanceof Error ? err.message : 'Unknown error'}`,
          variant: "destructive"
        })
      }
    } else {
      toast({
        title: "Download failed",
        description: "No audio URL available for this call",
        variant: "destructive"
      })
    }
  }

  const formatTime = (time: number) => {
    if (!isFinite(time)) return "0:00"
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const copyToClipboard = async (text: string, type: string) => {
    await navigator.clipboard.writeText(text)
    toast({
      description: `${type} copied to clipboard`,
    })
  }

  // Add a helper function to safely get customer name
  const getCustomerName = (call: Call) => {
    try {
      // First, try to get customer name from dynamic_variables
      if (call.dynamic_variables) {
        let customerName = null

        // If it's already an object, use it directly
        if (typeof call.dynamic_variables === 'object') {
          customerName = call.dynamic_variables.customer_name
        }

        // If it's a string, parse it
        if (typeof call.dynamic_variables === 'string') {
          const variables = JSON.parse(call.dynamic_variables)
          customerName = variables.customer_name
        }

        if (customerName) return customerName
      }

      // Fallback: Try to extract name from transcript
      if (call.transcript) {
        // Look for patterns like "My name is [Name]" or "I'm [Name]"
        const namePatterns = [
          /my name is ([A-Za-z\s]+)/i,
          /i'm ([A-Za-z\s]+)/i,
          /this is ([A-Za-z\s]+)/i,
          /name is ([A-Za-z\s]+)/i
        ]

        for (const pattern of namePatterns) {
          const match = call.transcript.match(pattern)
          if (match && match[1]) {
            const extractedName = match[1].trim()
            // Only return if it looks like a reasonable name (not too long, contains letters)
            if (extractedName.length <= 50 && /^[A-Za-z\s]+$/.test(extractedName)) {
              return extractedName
            }
          }
        }
      }

      return 'Unknown'
    } catch (e) {
      console.error('Error parsing dynamic_variables:', e)
      return 'Unknown'
    }
  }

  return (
    <div className="p-8 max-w-[1600px] mx-auto">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold">Call History</h1>
        <div className="flex items-center gap-2">
          {dateParam && (
            <div className="text-sm text-muted-foreground">
              Showing calls for: {new Date(dateParam).toLocaleDateString()}
            </div>
          )}
          <AgentSelector />
          <TimeframeSelector />
        </div>
      </div>

      {/* Update Filter Bar */}
      <Card className="p-4 mb-4">
        <div className="flex items-center gap-4">

          <Select
            value={filters.duration}
            onValueChange={(value) => setFilters(prev => ({ ...prev, duration: value }))}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by Duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={FILTER_VALUES.ALL.DURATION}>All Durations</SelectItem>
              {Object.entries(DURATION_RANGES).map(([key, { label }]) => (
                <SelectItem key={key} value={key}>{label}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.sentiment}
            onValueChange={(value) => setFilters(prev => ({ ...prev, sentiment: value }))}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by Sentiment" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={FILTER_VALUES.ALL.SENTIMENT}>All Sentiments</SelectItem>
              <SelectItem value="Positive">Positive</SelectItem>
              <SelectItem value="Neutral">Neutral</SelectItem>
              <SelectItem value="Negative">Negative</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.success}
            onValueChange={(value) => setFilters(prev => ({ ...prev, success: value }))}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by Success" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={FILTER_VALUES.ALL.SUCCESS}>All Calls</SelectItem>
              <SelectItem value="true">Successful</SelectItem>
              <SelectItem value="false">Unsuccessful</SelectItem>
            </SelectContent>
          </Select>

          {Object.values(filters).some(value =>
            !Object.values(FILTER_VALUES.ALL).includes(value)
          ) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="ml-auto"
            >
              <X className="h-4 w-4 mr-2" />
              Reset Filters
            </Button>
          )}
        </div>
      </Card>



      <Card className="p-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('start_timestamp')}>
                  Time
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('customer_name')}>
                  Customer
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('agent_name')}>
                  Agent
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('duration_seconds')}>
                  Duration
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Sentiment</TableHead>
              <TableHead>Success</TableHead>
              <TableHead className="text-right">
                <Button variant="ghost" onClick={() => handleSort('cost')}>
                  Cost
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedCalls.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  {calls.length === 0
                    ? "No calls found for the selected time period."
                    : "No calls match the current filters."
                  }
                </TableCell>
              </TableRow>
            ) : (
              sortedCalls.map((call) => (
                <TableRow
                  key={call.call_id}
                  className="cursor-pointer hover:bg-accent/50"
                  onClick={() => setSelectedCall(call)}
                >
                  <TableCell>{new Date(call.start_timestamp).toLocaleString()}</TableCell>
                  <TableCell className="font-medium">{getCustomerName(call)}</TableCell>
                  <TableCell>{call.agent_name}</TableCell>
                  <TableCell>
                    {`${Math.floor(call.duration_seconds / 60)}m ${call.duration_seconds % 60}s`}
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                      ${call.user_sentiment === 'Positive' ? 'bg-green-100 text-green-800' :
                        call.user_sentiment === 'Negative' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'}`}>
                      {call.user_sentiment}
                    </span>
                  </TableCell>
                  <TableCell>
                    {call.call_successful ?
                      <span className="text-green-600">✓</span> :
                      <span className="text-red-600">✗</span>
                    }
                  </TableCell>
                  <TableCell className="text-right">
                    ${(call.cost / 100).toFixed(2)}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      <Sheet open={!!selectedCall} onOpenChange={() => setSelectedCall(null)}>
        <SheetContent
          className="!w-[768px] !max-w-none overflow-hidden flex flex-col"
          side="right"
        >
          <SheetHeader className="flex-shrink-0 !w-[719px]">
            <SheetTitle>
              {selectedCall ? `Call Details - ${new Date(selectedCall.start_timestamp).toLocaleString()}` : 'Call Details'}
            </SheetTitle>
          </SheetHeader>

          {selectedCall && (
            <ScrollArea className="flex-1 !w-[719px] h-[calc(100vh-4rem)] pr-4">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold">{new Date(selectedCall.start_timestamp).toLocaleString()} phone_call</h2>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
                      onClick={async () => {
                        try {
                          const blob = await generatePDF(selectedCall)
                          const url = URL.createObjectURL(blob)
                          const link = document.createElement('a')
                          link.href = url
                          link.download = `call-report-${selectedCall.call_id}.pdf`
                          document.body.appendChild(link)
                          link.click()
                          document.body.removeChild(link)
                          URL.revokeObjectURL(url)
                        } catch (error) {
                          console.error('Error generating PDF:', error)
                          toast({
                            title: "Error",
                            description: "Failed to generate PDF report",
                            variant: "destructive"
                          })
                        }
                      }}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Download Report
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => setSelectedCall(null)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Call Details Section */}
                <div className="space-y-1 mb-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <span>Agent: Optional Employee Benefits Expert(age...9e5)</span>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>Call ID: cal...1b6</span>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <div>Phone Call: {selectedCall.customer_phone || '+13192585090'} → {selectedCall.agent_phone || '+13196218396'}</div>
                  <div>Duration: {new Date(selectedCall.start_timestamp).toLocaleString()} - {`${Math.floor(selectedCall.duration_seconds / 60)}m ${selectedCall.duration_seconds % 60}s`}</div>
                  <div>Cost: ${(selectedCall.cost / 100).toFixed(3)}</div>
                </div>

                {/* Audio Player Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Audio Recording</h3>
                    <Button variant="ghost" size="sm" onClick={handleDownload}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>

                  <audio
                    ref={audioRef}
                    src={selectedCall?.recording_url || selectedCall?.audio_url}
                    onTimeUpdate={handleTimeUpdate}
                    onLoadedMetadata={handleLoadedMetadata}
                    onEnded={() => setIsPlaying(false)}
                    onError={(e) => {
                      const audioElement = e.currentTarget as HTMLAudioElement
                      console.error('Audio error:', {
                        error: e,
                        networkState: audioElement.networkState,
                        readyState: audioElement.readyState,
                        src: audioElement.src
                      })
                      toast({
                        title: "Audio Error",
                        description: "Failed to load audio file. Please check console for details.",
                        variant: "destructive"
                      })
                    }}
                    crossOrigin="anonymous"
                  />

                  <div className="space-y-2">
                    <div
                      className="h-2 bg-secondary rounded-full cursor-pointer"
                      onClick={handleProgressClick}
                    >
                      <div
                        className="h-full bg-primary rounded-full"
                        style={{ width: `${progress}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePlayPause}
                      >
                        {isPlaying ? (
                          <Pause className="h-4 w-4" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </Button>
                      <div className="text-sm text-muted-foreground">
                        {formatTime(currentTime)} / {formatTime(duration)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Conversation Analysis Section */}
                <div className="bg-muted rounded-lg p-6">
                  <h3 className="text-base font-medium mb-4">Conversation Analysis</h3>
                  <div className="text-sm">
                    <p className="text-muted-foreground mb-4">Preset</p>
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        <span>Call Successful</span>
                        <span className="ml-auto bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">
                          Successful
                        </span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2" />
                        <span>Call Status</span>
                        <span className="ml-auto text-muted-foreground">
                          Ended
                        </span>
                      </div>
                      <div className="flex items-center">
                        <UserCircle2 className="h-4 w-4 mr-2" />
                        <span>User Sentiment</span>
                        <span className="ml-auto bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">
                          Positive
                        </span>
                      </div>
                      <div className="flex items-center">
                        <PhoneOff className="h-4 w-4 mr-2" />
                        <span>Disconnection Reason</span>
                        <div className="ml-auto flex items-center">
                          <span className="text-muted-foreground">User_hangup</span>
                          <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Timer className="h-4 w-4 mr-2" />
                        <span>End to End Latency</span>
                        <div className="ml-auto flex items-center">
                          <span className="text-muted-foreground">2826ms</span>
                          <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Summary Section */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Summary</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(selectedCall.call_summary, 'Summary')}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                  <p className="text-muted-foreground">{selectedCall.call_summary}</p>
                </div>

                {/* Transcript Section */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Transcript</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(selectedCall.transcript, 'Transcript')}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                  <div className="whitespace-pre-wrap text-sm bg-muted p-6 rounded-lg">
                    {formatTranscript(selectedCall.transcript)}
                  </div>
                </div>
              </div>
            </ScrollArea>
          )}
        </SheetContent>
      </Sheet>
    </div>
  )
}

// Main component with Suspense
export default function CallHistory() {
  return (
    <Suspense fallback={
      <div className="p-8 max-w-[1600px] mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-2xl font-semibold">Loading Call History...</h1>
        </div>
        <Card className="p-6">
          <div className="h-96 flex items-center justify-center">
            Loading...
          </div>
        </Card>
      </div>
    }>
      <CallHistoryContent />
    </Suspense>
  )
}