"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Check,
  ChevronRight,
  Sparkles,
  User,
  Phone,
  Mic,
  Brain,
  Database,
  Wrench,
  GraduationCap,
  Rocket
} from "lucide-react";
import { AgentTemplate } from '@/lib/agent-templates';
import TemplateSelectionStep from '@/components/agent-builder/TemplateSelectionStep';
import BasicInfoStep from '@/components/agent-builder/BasicInfoStep';
import AgentTypeStep, { AgentType } from '@/components/agent-builder/AgentTypeStep';
import VoiceSelectionStep from '@/components/agent-builder/VoiceSelectionStep';
import SmartBehaviorStep from '@/components/agent-builder/SmartBehaviorStep';
import KnowledgeBaseStep from '@/components/agent-builder/KnowledgeBaseStep';
import ToolsConfigurationStep, { Tool } from '@/components/agent-builder/ToolsConfigurationStep';
import TrainingStep from '@/components/agent-builder/TrainingStep';
import AgentCreationStep from '@/components/agent-builder/AgentCreationStep';
import voicesData from '@/components/agent-builder/retellai-voices-list.json';

const STEPS = [
  {
    id: 1,
    name: 'Template',
    title: 'Choose Template',
    description: 'Select a starting point',
    icon: Sparkles,
    component: 'template'
  },
  {
    id: 2,
    name: 'Basic Info',
    title: 'Basic Information',
    description: 'Name and description',
    icon: User,
    component: 'basic'
  },
  {
    id: 3,
    name: 'Type',
    title: 'Agent Type',
    description: 'Choose interaction mode',
    icon: Phone,
    component: 'type'
  },
  {
    id: 4,
    name: 'Voice',
    title: 'Voice Selection',
    description: 'Choose agent voice',
    icon: Mic,
    component: 'voice'
  },
  {
    id: 5,
    name: 'Behavior',
    title: 'Agent Behavior',
    description: 'Configure personality',
    icon: Brain,
    component: 'behavior'
  },
  {
    id: 6,
    name: 'Knowledge',
    title: 'Knowledge Base',
    description: 'Add information sources',
    icon: Database,
    component: 'knowledge'
  },
  {
    id: 7,
    name: 'Tools',
    title: 'Tools & Functions',
    description: 'Configure agent capabilities',
    icon: Wrench,
    component: 'tools'
  },
  {
    id: 8,
    name: 'Training',
    title: 'Training Examples',
    description: 'Provide examples',
    icon: GraduationCap,
    component: 'training'
  },
  {
    id: 9,
    name: 'Deploy',
    title: 'Create & Deploy',
    description: 'Launch your agent',
    icon: Rocket,
    component: 'creation'
  }
];

interface Voice {
  voice_id: string;
  voice_type: string;
  standard_voice_type?: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

interface Agent {
  id: string;
  name: string;
  description: string;
  status: "active" | "inactive";
  lastModified: string;
  knowledgeBases: number;
  totalCalls: number;
  voice?: string;
  phone?: string;
  editedBy?: string;
  type?: string;
  voiceId?: string;
  behavior?: string;
}

function CreateAgentContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);
  const [selectedVoice, setSelectedVoice] = useState<Voice | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    agentType: 'voice_call', // 'voice_call', 'web_call', 'chat', or 'multi_modal'
    voiceId: '',
    behavior: '',
    knowledgeBase: [],
    tools: [],
    trainingExamples: []
  });

  // Initialize from URL params
  useEffect(() => {
    const stepParam = searchParams.get('step');
    const templateParam = searchParams.get('template');

    if (stepParam) {
      const step = parseInt(stepParam);
      if (step >= 1 && step <= STEPS.length) {
        setCurrentStep(step);
      }
    }

    if (templateParam) {
      // Load template from URL - you'd implement this based on your template system
      console.log('Loading template:', templateParam);
    }
  }, [searchParams]);

  const currentStepData = STEPS.find(step => step.id === currentStep);

  // Calculate progress in multiples of 5%
  const calculateProgress = (step: number, totalSteps: number) => {
    const stepProgress = [10, 20, 30, 45, 60, 70, 80, 90, 100];
    return stepProgress[step - 1] || 0;
  };

  const progress = calculateProgress(currentStep, STEPS.length);

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      // Update URL
      const params = new URLSearchParams(searchParams);
      params.set('step', nextStep.toString());
      if (selectedTemplate) {
        params.set('template', selectedTemplate.id);
      }
      router.push(`/agent-builder/create?${params.toString()}`);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      // Update URL
      const params = new URLSearchParams(searchParams);
      params.set('step', prevStep.toString());
      if (selectedTemplate) {
        params.set('template', selectedTemplate.id);
      }
      router.push(`/agent-builder/create?${params.toString()}`);
    }
  };

  const handleStepClick = (stepId: number) => {
    // Allow navigation to previous steps or current step
    if (stepId <= currentStep) {
      setCurrentStep(stepId);
      // Update URL
      const params = new URLSearchParams(searchParams);
      params.set('step', stepId.toString());
      if (selectedTemplate) {
        params.set('template', selectedTemplate.id);
      }
      router.push(`/agent-builder/create?${params.toString()}`);
    }
  };

  const handleVoiceSelect = (voice: Voice) => {
    setSelectedVoice(voice);
    setFormData(prev => ({ ...prev, voiceId: voice.voice_id }));
  };

  const handleFormDataChange = (data: { name: string; description: string }) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const handleSaveAgent = (agentData: Agent) => {
    // Handle agent creation success
    console.log('Agent created:', agentData);
    router.push('/agent-builder');
  };

  const handleCancel = () => {
    router.push('/agent-builder');
  };

  const renderStepContent = () => {
    switch (currentStepData?.component) {
      case 'template':
        return (
          <TemplateSelectionStep
            onSelectTemplate={setSelectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
            selectedTemplate={selectedTemplate}
          />
        );
      case 'basic':
        return (
          <BasicInfoStep
            selectedTemplate={selectedTemplate}
            formData={{ name: formData.name, description: formData.description }}
            onFormDataChange={handleFormDataChange}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'type':
        return (
          <AgentTypeStep
            selectedTemplate={selectedTemplate}
            selectedType={formData.agentType as AgentType}
            onTypeChange={(type) => setFormData(prev => ({ ...prev, agentType: type }))}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'voice':
        return (
          <VoiceSelectionStep
            selectedTemplate={selectedTemplate}
            selectedVoice={selectedVoice}
            agentType={formData.agentType}
            onVoiceSelect={handleVoiceSelect}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'behavior':
        return (
          <SmartBehaviorStep
            selectedTemplate={selectedTemplate}
            behavior={formData.behavior}
            onBehaviorChange={(behavior) => setFormData(prev => ({ ...prev, behavior }))}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'knowledge':
        return (
          <KnowledgeBaseStep
            selectedTemplate={selectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'tools':
        return (
          <ToolsConfigurationStep
            selectedTemplate={selectedTemplate}
            tools={formData.tools}
            onToolsChange={(tools) => setFormData(prev => ({ ...prev, tools }))}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'training':
        return (
          <TrainingStep
            selectedTemplate={selectedTemplate}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 'creation':
        return (
          <AgentCreationStep
            agentData={{
              name: formData.name,
              description: formData.description,
              agentType: formData.agentType,
              voiceId: formData.voiceId,
              behavior: formData.behavior,
              selectedTemplate,
              selectedVoice,
              knowledgeBaseSelections: formData.knowledgeBase,
              tools: formData.tools,
              trainingExamples: formData.trainingExamples
            }}
            onSuccess={(agentId) => {
              console.log('Agent created with ID:', agentId);
              router.push('/agent-builder');
            }}
            onBack={handleBack}
          />
        );
      default:
        return (
          <div className="max-w-3xl mx-auto py-8">
            <h2 className="text-2xl font-bold text-foreground mb-4">{currentStepData?.title}</h2>
            <p className="text-muted-foreground mb-8">{currentStepData?.description}</p>
            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button onClick={handleNext}>
                Next Step
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background flex overflow-hidden">
      {/* Sidebar Navigation */}
      <div className="w-80 bg-card border-r border-border flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-foreground">Create AI Agent</h1>
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Steps */}
        <div className="flex-1 p-4 space-y-2">
          {STEPS.map((step) => {
            const Icon = step.icon;
            const isActive = step.id === currentStep;
            const isCompleted = step.id < currentStep;
            const isAccessible = step.id <= currentStep;

            return (
              <button
                key={step.id}
                onClick={() => handleStepClick(step.id)}
                disabled={!isAccessible}
                className={`w-full p-4 rounded-lg text-left transition-all duration-200 ${
                  isActive
                    ? 'bg-primary text-primary-foreground shadow-lg'
                    : isCompleted
                    ? 'bg-muted text-muted-foreground hover:bg-muted/80'
                    : isAccessible
                    ? 'bg-muted text-muted-foreground hover:bg-muted/80'
                    : 'bg-muted/50 text-muted-foreground/50 cursor-not-allowed'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`flex-shrink-0 ${
                    isCompleted ? 'text-green-500' : isActive ? 'text-primary-foreground' : 'text-muted-foreground'
                  }`}>
                    {isCompleted ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{step.name}</div>
                    <div className="text-xs opacity-75 truncate">{step.description}</div>
                  </div>
                  {isActive && (
                    <ChevronRight className="h-4 w-4 flex-shrink-0" />
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Template Info */}
        {selectedTemplate && (
          <div className="p-4 border-t border-border">
            <div className="bg-primary/10 border border-primary/20 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-lg">{selectedTemplate.icon}</span>
                <span className="text-foreground font-medium text-sm">{selectedTemplate.name}</span>
              </div>
              <p className="text-muted-foreground text-xs">{selectedTemplate.description}</p>
              <div className="flex flex-wrap gap-1 mt-2">
                <Badge variant="secondary" className="text-xs">
                  {selectedTemplate.category}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {selectedTemplate.difficulty}
                </Badge>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="min-h-full bg-background">
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
}

export default function CreateAgentPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-foreground">Loading agent builder...</p>
        </div>
      </div>
    }>
      <CreateAgentContent />
    </Suspense>
  );
}
