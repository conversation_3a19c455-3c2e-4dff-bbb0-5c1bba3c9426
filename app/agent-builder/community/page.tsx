"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Bot,
  Search,
  Star,
  StarHalf,
  Download,
  GitFork,
  Clock,
  Filter,
  ArrowUpDown,
  Share2,
  Verified,
  Trophy,
  ThumbsUp
} from "lucide-react"
import { VoiceAgentIcon } from "@/components/ui/voice-agent-icon"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"

interface CommunityAgent {
  id: string
  name: string
  description: string
  creator: {
    name: string
    agency: string
    location: string
  }
  stats: {
    stars: number
    downloads: number
    rating: number
  }
  tags: string[]
  createdAt: string
  lastUpdated: string
  version: string
  isVerified: boolean
}

// Helper function to generate random rating between 3.2 and 4.8
const generateRating = () => (Math.random() * (4.8 - 3.2) + 3.2).toFixed(1)

const mockCommunityAgents: CommunityAgent[] = [
  {
    id: "1",
    name: "Group Benefits Expert",
    description: "Specialized in handling inquiries about group insurance benefits, including group accident, hospital indemnity, and critical illness. Features deep knowledge of group policies, rates, and enrollment procedures.",
    creator: {
      name: "<PERSON> Chen",
      agency: "Pacific Northwest Benefits",
      location: "Seattle, WA"
    },
    stats: {
      stars: 485,
      downloads: 1243,
      rating: 4.7
    },
    tags: ["group benefits", "accident", "hospital indemnity", "critical illness"],
    createdAt: "2023-09-15",
    lastUpdated: "2024-02-10",
    version: "2.3.0",
    isVerified: true
  },
  {
    id: "2",
    name: "Short-Term Disability Specialist",
    description: "Expert AI agent for short-term disability insurance consultations. Includes comprehensive knowledge base of policy details, claims processing, and benefit calculations.",
    creator: {
      name: "Marcus Johnson",
      agency: "Southeast Insurance Solutions",
      location: "Atlanta, GA"
    },
    stats: {
      stars: 372,
      downloads: 892,
      rating: 4.4
    },
    tags: ["disability", "STD", "claims", "benefits calculation"],
    createdAt: "2023-11-02",
    lastUpdated: "2024-01-28",
    version: "1.8.5",
    isVerified: true
  },
  {
    id: "3",
    name: "Cancer Care Coverage Assistant",
    description: "Specialized in cancer insurance policies, including policy features, wellness benefits, and claims guidance. Enhanced with recent policy updates and claim statistics.",
    creator: {
      name: "Lisa Rodriguez",
      agency: "Southwest Benefits Group",
      location: "Phoenix, AZ"
    },
    stats: {
      stars: 291,
      downloads: 756,
      rating: 4.2
    },
    tags: ["cancer insurance", "wellness", "claims", "coverage"],
    createdAt: "2023-12-08",
    lastUpdated: "2024-02-15",
    version: "1.4.2",
    isVerified: false
  },
  {
    id: "4",
    name: "Dental & Vision Pro",
    description: "Comprehensive agent for dental and vision insurance inquiries. Features detailed coverage explanations, network information, and cost comparisons.",
    creator: {
      name: "Robert Kim",
      agency: "Midwest Insurance Partners",
      location: "Chicago, IL"
    },
    stats: {
      stars: 328,
      downloads: 945,
      rating: 4.6
    },
    tags: ["dental", "vision", "networks", "coverage comparison"],
    createdAt: "2023-10-20",
    lastUpdated: "2024-02-01",
    version: "2.0.1",
    isVerified: true
  },
  {
    id: "5",
    name: "Life Insurance Navigator",
    description: "Specialized in whole life and term life insurance consultations. Includes premium calculators, policy comparisons, and beneficiary management guidance.",
    creator: {
      name: "Emily Thompson",
      agency: "Northeast Benefits Solutions",
      location: "Boston, MA"
    },
    stats: {
      stars: 412,
      downloads: 1089,
      rating: 4.8
    },
    tags: ["life insurance", "whole life", "term life", "premium calculation"],
    createdAt: "2023-08-30",
    lastUpdated: "2024-02-18",
    version: "2.5.0",
    isVerified: true
  },
  {
    id: "6",
    name: "Hospital Care Specialist",
    description: "Expert in hospital indemnity insurance, featuring admission benefits, recovery benefits, and ambulance benefits knowledge base. Includes real-world claim scenarios.",
    creator: {
      name: "David Martinez",
      agency: "Gulf Coast Insurance Group",
      location: "Miami, FL"
    },
    stats: {
      stars: 267,
      downloads: 678,
      rating: 3.9
    },
    tags: ["hospital", "indemnity", "admission benefits", "recovery"],
    createdAt: "2024-01-05",
    lastUpdated: "2024-02-12",
    version: "1.2.0",
    isVerified: false
  }
]

// Add our AFLAC Benefits Agent
const aflacAgent: CommunityAgent = {
  id: "aflac-1",
  name: "Optional Employee Benefits Expert",
  description: "Comprehensive AI agent specializing in Aflac's optional employee benefits portfolio. Expert in accident, hospital indemnity, critical illness, dental, vision, life, and disability insurance. Features advanced policy explanation, claims guidance, and enrollment support.",
  creator: {
    name: "Virtual Assistant Labs",
    agency: "Aflac Innovation Labs",
    location: "Columbus, GA"
  },
  stats: {
    stars: 892,
    downloads: 2456,
    rating: 4.9
  },
  tags: [
    "accident",
    "hospital indemnity",
    "critical illness",
    "dental",
    "vision",
    "life insurance",
    "disability"
  ],
  createdAt: "2023-06-15",
  lastUpdated: "2024-02-25",
  version: "3.5.0",
  isVerified: true
}

export default function CommunityAgentsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("stars")

  const filteredAgents = mockCommunityAgents
    .filter(agent => {
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase()
        return (
          agent.name.toLowerCase().includes(searchLower) ||
          agent.description.toLowerCase().includes(searchLower) ||
          agent.tags.some(tag => tag.toLowerCase().includes(searchLower))
        )
      }
      return true
    })
    .filter(agent => {
      if (selectedCategory === "all") return true
      return agent.tags.includes(selectedCategory)
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "stars":
          return b.stats.stars - a.stats.stars
        case "downloads":
          return b.stats.downloads - a.stats.downloads
        case "recent":
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
        default:
          return 0
      }
    })

  return (
    <div className="p-8 max-w-[1600px] mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-semibold mb-2">Community AI Agents</h1>
          <p className="text-muted-foreground">
            Explore and use AI agents created by Aflac agencies across the country
          </p>
        </div>
        <Button>
          <Share2 className="h-4 w-4 mr-2" />
          Share Your Agent
        </Button>
      </div>

      {/* Featured Agent */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Trophy className="h-5 w-5 text-yellow-500" />
          Featured Agent
        </h2>
        <Card className="p-6 border-2 border-primary/50 transition-all duration-200 hover:shadow-lg hover:scale-[1.02] hover:border-primary bg-blue-50/50 dark:bg-blue-950/10">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary/10">
                <VoiceAgentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">{aflacAgent.name}</h3>
                  <span className="bg-primary/10 text-primary text-xs font-medium px-2 py-0.5 rounded flex items-center gap-1">
                    <Verified className="h-3 w-3" />
                    Official
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">{aflacAgent.description}</p>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            {aflacAgent.tags.map((tag) => (
              <span
                key={tag}
                className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs"
              >
                {tag}
              </span>
            ))}
          </div>

          <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
            <div>
              <p className="text-muted-foreground">Creator</p>
              <p className="font-medium">{aflacAgent.creator.name}</p>
              <p className="text-xs text-muted-foreground">{aflacAgent.creator.agency}</p>
              <p className="text-xs text-muted-foreground">{aflacAgent.creator.location}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Version</p>
              <p className="font-medium">{aflacAgent.version}</p>
              <p className="text-xs text-muted-foreground">
                Updated {new Date(aflacAgent.lastUpdated).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-yellow-400" />
                <span>{aflacAgent.stats.stars}</span>
              </div>
              <div className="flex items-center gap-1">
                <Download className="h-4 w-4 text-muted-foreground" />
                <span>{aflacAgent.stats.downloads}</span>
              </div>
              <div className="flex items-center gap-1">
                <ThumbsUp className="h-4 w-4 text-green-500" />
                <span className="font-medium">{aflacAgent.stats.rating}</span>
              </div>
            </div>
            <Button variant="default" className="bg-primary hover:bg-primary/90">
              Use This Agent
            </Button>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search agents by name, description, or tags..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="group benefits">Group Benefits</SelectItem>
            <SelectItem value="disability">Disability</SelectItem>
            <SelectItem value="cancer insurance">Cancer Insurance</SelectItem>
            <SelectItem value="dental">Dental & Vision</SelectItem>
            <SelectItem value="life insurance">Life Insurance</SelectItem>
            <SelectItem value="hospital">Hospital Care</SelectItem>
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-[180px]">
            <ArrowUpDown className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="stars">Most Stars</SelectItem>
            <SelectItem value="downloads">Most Downloads</SelectItem>
            <SelectItem value="recent">Recently Updated</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Community Agents Grid */}
      <div className="mb-4">
        <h2 className="text-lg font-semibold mb-4">Community Agents</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredAgents.map((agent) => (
          <Card
            key={agent.id}
            className="p-6 transition-all duration-200 hover:shadow-lg hover:scale-[1.02] hover:border-primary/50 bg-blue-50/50 dark:bg-blue-950/10"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-primary/10">
                  <VoiceAgentIcon className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{agent.name}</h3>
                    {agent.isVerified && (
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                        Verified
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{agent.description}</p>
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              {agent.tags.map((tag) => (
                <span
                  key={tag}
                  className="bg-secondary text-secondary-foreground px-2 py-1 rounded-full text-xs"
                >
                  {tag}
                </span>
              ))}
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
              <div>
                <p className="text-muted-foreground">Creator</p>
                <p className="font-medium">{agent.creator.name}</p>
                <p className="text-xs text-muted-foreground">{agent.creator.agency}</p>
                <p className="text-xs text-muted-foreground">{agent.creator.location}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Version</p>
                <p className="font-medium">{agent.version}</p>
                <p className="text-xs text-muted-foreground">
                  Updated {new Date(agent.lastUpdated).toLocaleDateString()}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-400" />
                  <span>{agent.stats.stars}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4 text-muted-foreground" />
                  <span>{agent.stats.downloads}</span>
                </div>
                <div className="flex items-center gap-1">
                  <ThumbsUp className="h-4 w-4 text-green-500" />
                  <span className="font-medium">{agent.stats.rating}</span>
                </div>
              </div>
              <Button>
                Use This Agent
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}