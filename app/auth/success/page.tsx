"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Loader2 } from "lucide-react";

function AuthSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const next = searchParams.get('next') || '/';

  useEffect(() => {
    const handleSuccess = async () => {
      try {
        // Check if we have a valid session
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          // Try to ensure user is assigned to organization
          try {
            const response = await fetch('/api/auth/ensure-organization-assignment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                userId: session.user.id,
                email: session.user.email
              }),
            });

            if (!response.ok) {
              console.warn('Failed to ensure organization assignment, but continuing...');
            }
          } catch (err) {
            console.warn('Organization assignment check failed, but continuing...', err);
          }

          setLoading(false);

          // Wait a moment to show success, then redirect
          setTimeout(() => {
            router.push(next);
          }, 2000);
        } else {
          // No session, redirect to sign in
          router.push('/');
        }
      } catch (error) {
        console.error('Success page error:', error);
        router.push('/');
      }
    };

    handleSuccess();
  }, [router, next]);

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader className="text-center">
          <CardTitle className="text-white">
            Welcome!
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          {loading ? (
            <div className="space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
              <p className="text-gray-400">
                Setting up your account...
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <CheckCircle className="h-8 w-8 mx-auto text-green-500" />
              <div>
                <p className="text-green-400 font-medium">
                  Successfully signed in!
                </p>
                <p className="text-gray-400 text-sm mt-1">
                  Redirecting you to the dashboard...
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default function AuthSuccess() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-900 border-gray-800">
          <CardHeader className="text-center">
            <CardTitle className="text-white">Loading...</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          </CardContent>
        </Card>
      </div>
    }>
      <AuthSuccessContent />
    </Suspense>
  );
}
