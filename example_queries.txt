-- Get all calls for our test organization
SELECT c.*, a.name as agent_name, o.name as org_name
FROM calls c
JOIN agents a ON c.agent_id = a.id
JOIN organizations o ON a.organization_id = o.id
WHERE o.id = 'd0816f44-4576-4b6f-8c45-a80464f0a6c8';

-- Get call statistics by agent
SELECT
  a.name as agent_name,
  count(*) as total_calls,
  avg(duration_ms)::float / 1000 / 60 as avg_duration_minutes,
  count(*) filter (where c.call_status = 'ended') as completed_calls
FROM calls c
JOIN agents a ON c.agent_id = a.id
GROUP BY a.id, a.name;

-- Search transcripts
SELECT c.*, a.name as agent_name
FROM calls c
JOIN agents a ON c.agent_id = a.id
WHERE to_tsvector('english', c.transcript) @@ to_tsquery('english', 'benefits');

-- Get dynamic variables
SELECT
  c.call_id,
  c.dynamic_variables->>'customer_name' as customer_name
FROM calls c
WHERE c.dynamic_variables->>'customer_name' IS NOT NULL;