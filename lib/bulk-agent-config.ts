// Comprehensive Agent Configuration Interface
export interface BulkAgentConfig {
  // Basic Agent Information
  agent: {
    name: string;
    description: string;
    language?: string;
    voice_id: string;
    webhook_url?: string;
  };

  // LLM Configuration
  llm: {
    general_prompt: string;
    begin_message?: string;
    model?: 'gpt-4o' | 'gpt-4o-mini' | 'gpt-4.1' | 'gpt-4.1-mini' | 'claude-3.7-sonnet' | 'claude-3.5-haiku';
    model_temperature?: number;
    tool_call_strict_mode?: boolean;
  };

  // Knowledge Bases
  knowledge_bases?: Array<{
    name: string;
    description?: string;
    sources: Array<{
      type: 'document' | 'url';
      content?: string; // For document type
      url?: string; // For URL type
      file_name?: string; // For document type
    }>;
  }>;

  // Tools Configuration
  tools?: Array<{
    type: 'end_call' | 'transfer_call' | 'custom' | 'press_digit' | 'check_availability_cal' | 'book_appointment_cal';
    name: string;
    description: string;
    // Tool-specific configurations
    transfer_destination?: {
      type: 'predefined' | 'inferred';
      value?: 'voicemail' | 'operator';
      number?: string;
      prompt?: string;
    };
    digit?: string; // For press_digit
    url?: string; // For custom tools
    speak_after_execution?: boolean;
    speak_during_execution?: boolean;
    // Calendar tool specific
    calendar_url?: string;
    cal_api_key?: string;
    event_type_id?: number;
  }>;

  // Phone Number Configuration (optional)
  phone_number?: {
    area_code: number;
    nickname?: string;
  };

  // Organization Assignment
  organization?: {
    organization_id?: string;
    type?: 'inbound_voice' | 'outbound_voice' | 'web_call';
  };

  // Metadata
  metadata?: {
    template_id?: string;
    category?: string;
    tags?: string[];
    created_by?: string;
    notes?: string;
  };
}

// Result interface for bulk creation
export interface BulkAgentResult {
  success: boolean;
  agent_id?: string;
  llm_id?: string;
  knowledge_base_ids?: string[];
  phone_number?: string;
  errors?: Array<{
    step: string;
    error: string;
  }>;
  warnings?: Array<{
    step: string;
    message: string;
  }>;
}

// Example configuration templates
export const AGENT_CONFIG_TEMPLATES = {
  INSURANCE_CUSTOMER_SERVICE: {
    agent: {
      name: "Insurance Customer Service Agent",
      description: "AI agent specialized in handling insurance customer inquiries, claims, and policy questions",
      language: "en-US",
      voice_id: "11labs-Adrian"
    },
    llm: {
      general_prompt: `You are a professional insurance customer service representative. You help customers with:
- Policy inquiries and explanations
- Claims status and filing assistance  
- Coverage questions and recommendations
- Billing and payment support
- General insurance guidance

Always be helpful, empathetic, and professional. If you cannot help with something, offer to transfer to a specialist.`,
      begin_message: "Hello! I'm your AI insurance assistant. How can I help you with your insurance needs today?",
      model: "gpt-4o-mini" as const,
      model_temperature: 0.7
    },
    knowledge_bases: [
      {
        name: "Insurance Policies Knowledge Base",
        description: "Comprehensive information about insurance policies, coverage types, and procedures",
        sources: [
          {
            type: "url" as const,
            url: "https://example.com/insurance-policies.pdf"
          }
        ]
      }
    ],
    tools: [
      {
        type: "transfer_call" as const,
        name: "Transfer to Claims Specialist",
        description: "Transfer customer to a human claims specialist for complex claim issues",
        transfer_destination: {
          type: "predefined" as const,
          value: "operator" as const,
          number: "+1234567890"
        }
      },
      {
        type: "end_call" as const,
        name: "End Call",
        description: "End the call when the customer's needs have been addressed"
      }
    ]
  },

  SALES_AGENT: {
    agent: {
      name: "Insurance Sales Agent",
      description: "AI agent focused on insurance sales, lead qualification, and appointment scheduling",
      language: "en-US", 
      voice_id: "11labs-Sarah"
    },
    llm: {
      general_prompt: `You are an insurance sales representative. Your goals are to:
- Qualify leads and understand customer needs
- Explain insurance products and benefits
- Schedule appointments with human agents
- Provide quotes and comparisons
- Build rapport and trust

Be consultative, not pushy. Focus on understanding needs before recommending solutions.`,
      begin_message: "Hi there! I'm calling to help you explore insurance options that might benefit you. Do you have a few minutes to chat?",
      model: "gpt-4o-mini" as const,
      model_temperature: 0.8
    },
    tools: [
      {
        type: "book_appointment_cal" as const,
        name: "Schedule Appointment",
        description: "Schedule an appointment with a human insurance agent",
        calendar_url: "https://cal.com/insurance-agent",
        cal_api_key: "your-cal-api-key",
        event_type_id: 123456
      }
    ]
  }
} as const;

// Validation function
export function validateAgentConfig(config: BulkAgentConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required fields
  if (!config.agent?.name) errors.push("Agent name is required");
  if (!config.agent?.voice_id) errors.push("Voice ID is required");
  if (!config.llm?.general_prompt) errors.push("LLM general prompt is required");

  // Voice ID validation (basic check)
  if (config.agent?.voice_id && !config.agent.voice_id.match(/^[a-zA-Z0-9_-]+$/)) {
    errors.push("Invalid voice ID format");
  }

  // Knowledge base validation
  if (config.knowledge_bases) {
    config.knowledge_bases.forEach((kb, index) => {
      if (!kb.name) errors.push(`Knowledge base ${index + 1} missing name`);
      if (!kb.sources || kb.sources.length === 0) {
        errors.push(`Knowledge base ${index + 1} has no sources`);
      }
    });
  }

  // Tools validation
  if (config.tools) {
    config.tools.forEach((tool, index) => {
      if (!tool.name) errors.push(`Tool ${index + 1} missing name`);
      if (!tool.description) errors.push(`Tool ${index + 1} missing description`);
      
      if (tool.type === 'transfer_call' && !tool.transfer_destination) {
        errors.push(`Transfer tool ${index + 1} missing transfer destination`);
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
